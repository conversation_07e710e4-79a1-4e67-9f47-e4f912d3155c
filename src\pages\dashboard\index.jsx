import React, { useState, useEffect, useContext } from "react";
import Header from "./NavHeader.jsx";
import GateWay from "./GateWay.jsx";
import ESL from "./ESL";
import RegisteredEsl from "./RegisteredEsl.jsx";
import RefreshSuccessRate from "./RefreshSuccessRate.jsx";
import TotalRefreshTime from "./TotalRefreshTime.jsx";
import DeviceDistribution from "./DeviceDistribution.jsx";
import { Grid, Typography, Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import LineCharts from "./LineCharts.jsx";
import AppContext from "@/context/AppContext";
import CommonUtil from "@/util/CommonUtils";
import { toast } from "react-toastify";
import { getDashboardData } from "@s/DashboardService";
function index() {
  const { t } = useTranslation();
  const { selectedClient } = useContext(AppContext);
  //头部开始时间和结束时间
  const [startTime, setStartTime] = useState(""); // 开始时间
  const [endTime, setEndTime] = useState(""); // 结束时间
  const [outletID, setOutletID] = useState("");
  const [dashBoardData, setDashBoardData] = useState([]);

  const loadBoardData = () => {
    let params = {
      startDate: startTime,
      endDate: endTime,
      outletId: outletID == "All" ? -1 : outletID,
      offsetId: dayjs().format("Z"),
    };

    getDashboardData(params).then((res) => {
      if (res?.data?.code === "LVLI0000") {
        setDashBoardData(res?.data?.data);
      } else {
        toast.error(res?.data?.message);
      }
    });
  };

  const [filters, setFilters] = useState({
    page: 0,
    rowsPerPage: 5,
    country: "",
    state: "",
    city: "",
    cityZone: "",
    outletType: "",
    ids: [],
    clientId: "",
    companyIds:
      CommonUtil.isEmptyString(localStorage.getItem("selectedClientId")) ||
      localStorage.getItem("selectedClientId") === "-1"
        ? []
        : [localStorage.getItem("selectedClientId")],
  });

  useEffect(() => {
    const v = JSON.parse(localStorage.getItem("selectedClient"));

    if (!CommonUtil.isEmpty(v) && v.id !== filters.companyIds[0]) {
      setFilters({ ...filters, companyIds: [v?.id] });
    } else if (CommonUtil.isEmpty(v)) {
      setFilters({ ...filters, companyIds: [] });
    }
  }, [selectedClient]);

  return (
    <React.Fragment>
      {/* <Grid>
        <Box
          fontSize={18}
          sx={{
            color: "rgba(194,196,191,1)",
            fontWeight: "600",
          }}
        >
          {t("dashboard.title")}
        </Box>
        <Box sx={{ fontWeight: 700, color: "#000", fontSize: "24px" }}>
          {t("dashboard.welcome_back")}
        </Box>
      </Grid> */}

      <Grid container spacing={1} mt={2}>
        <Header
          startTime={startTime}
          setStartTime={setStartTime}
          endTime={endTime}
          setEndTime={setEndTime}
          loadBoardData={loadBoardData}
          setOutletID={setOutletID}></Header>
      </Grid>

      <Grid container spacing={1} mt={2}>
        <Grid item container spacing={1} xs={12} sm={12} lg={12} md={12}>
          <Grid item lg={2.8} md={6} xs={12} sm={7}>
            <Grid
              sx={{
                height: "48%",
                background: "#FFF",
                borderRadius: "10px",
                border: "1px solid transparent", // 设置边框以使阴影可见
                boxShadow:
                  "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)", // 边框阴影
              }}>
              <RegisteredEsl dashBoardData={dashBoardData}></RegisteredEsl>
            </Grid>

            <Grid
              sx={{
                height: "49%",
                background: "#FFF",
                borderRadius: "10px",
                mt: 1,
                border: "1px solid transparent", // 设置边框以使阴影可见
                boxShadow:
                  "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)", // 边框阴影
              }}>
              <GateWay dashBoardData={dashBoardData}></GateWay>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6} lg={2.7} md={10}>
            <Grid
              sx={{
                height: "100%",
                background: "#FFF",
                borderRadius: "10px",
                border: "1px solid transparent", // 设置边框以使阴影可见
                boxShadow:
                  "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)", // 边框阴影
              }}>
              <ESL />
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6} lg={4.2} md={5}>
            <Grid
              sx={{
                background: "#FFF",
                borderRadius: "10px",
                border: "1px solid transparent",
                height: "100%",
                boxShadow:
                  "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)",
              }}>
              <DeviceDistribution
                dashBoardData={dashBoardData}></DeviceDistribution>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={6} lg={2.3} md={2}>
            <Grid
              sx={{
                background: "#FFF",
                borderRadius: "10px",
                border: "1px solid transparent",
                height: "100%",
                boxShadow:
                  "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)",
              }}>
              <TotalRefreshTime
                dashBoardData={dashBoardData}></TotalRefreshTime>
            </Grid>
          </Grid>

          <Grid
            container
            spacing={1}
            item
            mt={1}
            xs={12}
            sm={12}
            lg={12}
            md={12}
            sx={{
              height: "50%", // 下半部分占剩余高度的50%
              display: "flex",
              flexGrow: 1,
            }}>
            <Grid item xs={9}>
              <Grid
                sx={{
                  background: "#FFF",
                  borderRadius: "10px",
                  border: "1px solid transparent",
                  height: "100%",
                  boxShadow:
                    "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)",
                }}>
                <Grid display={"flex"} pt={3} pl={3}>
                  <Typography
                    variant="h5"
                    fontWeight={"bold"}
                    sx={{
                      color: "#000",
                      lineHeight: "50px",
                    }}>
                    {t("dashboard.refresh_events")}
                  </Typography>
                </Grid>

                <Grid display={"flex"} pt={-3} pb={1} pr={2} pl={2}>
                  <Typography
                    variant="h5"
                    fontWeight={"bold"}
                    sx={{
                      color: "#000",
                      textAlign: "center",
                      writingMode: "vertical-rl", // 竖排
                      textOrientation: "mixed", // 支持混合方向
                      // transform: "rotate(180deg)", // 如果需要反转，可以添加旋转
                    }}>
                    {t("dashboard.refresh_time")}
                  </Typography>

                  <LineCharts dashBoardData={dashBoardData}></LineCharts>

                  <Typography
                    variant="h5"
                    fontWeight={"bold"}
                    sx={{
                      color: "#000",
                      textAlign: "center",
                      writingMode: "vertical-rl", // 竖排
                      textOrientation: "mixed", // 支持混合方向
                      // transform: "rotate(180deg)", // 如果需要反转，可以添加旋转
                    }}>
                    {t("dashboard.success_rate")}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={3}>
              <Grid
                sx={{
                  background: "#FFF",
                  borderRadius: "10px",
                  border: "1px solid transparent",
                  height: "440px",
                  boxShadow:
                    "0 4px 8px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.1)",
                }}>
                <RefreshSuccessRate
                  dashBoardData={dashBoardData}></RefreshSuccessRate>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default index;
