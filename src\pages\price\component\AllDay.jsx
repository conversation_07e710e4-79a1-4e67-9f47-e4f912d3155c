import { Box, Grid, InputLabel, FormControlLabel, Radio } from "@mui/material";
import { useTranslation } from "react-i18next";
import TimePickerDate from "@c/TimePickerDate";
import { useEffect } from "react";
import dayjs from "dayjs";

function SetRepeatCondition(props) {
  const {
    hanlderAllDay,
    allday,
    fullDay,
    morningTime,
    afterTime,
    setMorningTime,
    setAfterTime,
  } = props;
  const { t } = useTranslation();
  // const [allday, setAllDay] = useState(true);
  // const [morningTime, setMorningTime] = useState(null);
  // const [afterTime, setAfterTime] = useState(null);
  // const hanlderAllDay = () => {
  //   if (allday === true) {
  //     setAllDay(false);
  //   } else {
  //     setAllDay(true);
  //     setMorningTime(dayjs().startOf("day"));
  //     setAfterTime(dayjs().set("hour", 23).set("minute", 59));
  //   }
  // };

  return (
    <Grid display={"flex"} pb={3}>
      <Box m={1}>
        <InputLabel shrink htmlFor="bootstrap-input">
          {t("events.startTime")}
        </InputLabel>
        <TimePickerDate
          disabled={fullDay}
          defaultValue={dayjs().startOf("day").format("HH:mm:ss")}
          value={morningTime}
          onChange={(e) => setMorningTime(e)}
          placeholder={
            morningTime === null ||
            morningTime === "Invalid Date" ||
            morningTime === undefined
              ? dayjs().startOf("day").format("HH:mm:ss")
              : morningTime
          }></TimePickerDate>
      </Box>

      <Box m={1}>
        <InputLabel shrink htmlFor="bootstrap-input">
          {t("common.start_time")}
        </InputLabel>
        <TimePickerDate
          disabled={fullDay}
          defaultValue={dayjs().endOf("day").format("HH:mm:ss")}
          value={afterTime}
          onChange={(e) => setAfterTime(e)}
          placeholder={
            afterTime === null ||
            afterTime === "Invalid Date" ||
            afterTime === undefined
              ? dayjs().endOf("day").format("HH:mm:ss")
              : afterTime
          }></TimePickerDate>
      </Box>

      <Box mt={5} ml={5}>
        <FormControlLabel
          control={
            <Radio
              id={`role-radio-1}`}
              checked={fullDay}
              onClick={hanlderAllDay}
              value={fullDay}
              name="selection"
            />
          }
          label={t("events.allDay")}
        />
      </Box>
    </Grid>
  );
}

export default SetRepeatCondition;
