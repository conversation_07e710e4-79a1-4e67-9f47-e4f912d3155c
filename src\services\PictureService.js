import request from "@/util/request";

const baseUrl = "/nt/v1";

export const getPage = (params) => {
  return request({
    url: `${baseUrl}/picture_library/page`,
    method: "GET",
    params: params,
  });
};

export const getList = (params) => {
  return request({
    url: `${baseUrl}/picture_library`,
    method: "GET",
    params: params,
  });
};

export const add = (params) => {
  return request({
    url: `${baseUrl}/picture_library`,
    method: "POST",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const edit = (params, id) => {
  return request({
    url: `${baseUrl}/picture_library/${id}`,
    method: "PUT",
    data: params,
    headers: {
      "Content-Type": "multipart/form-data", // 手动设置 Content-Type 为 multipart/form-data
    },
  });
};

export const getDetail = (id) => {
  return request({
    url: `${baseUrl}/picture_library/${id}`,
    method: "GET",
  });
};

export const deletes = (params) => {
  return request({
    url: `${baseUrl}/picture_library`,
    method: "DELETE",
    data: params,
  });
};
