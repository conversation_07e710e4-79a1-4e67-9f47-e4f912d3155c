import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Box, Divider } from "@mui/material";
import PieSvg from "./PIe";
function DeviceDistribution(props) {
  const { dashBoardData } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Box textAlign={"center"}>
        <Typography variant="h6" fontWeight={"bold"}>
          {t("dashboard.devices_distribution")}
        </Typography>
        <Typography style={{ color: "gray", fontSize: "14px" }}>
          {t("dashboard.esl_total_stores")}
        </Typography>
        <Grid container>
          <Grid
            item
            lg={5.5}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              textAlign: "center",
            }}>
            <Grid>
              <Typography variant="h2" fontWeight={"bold"}>
                {dashBoardData?.outletOfInstallDevice}
              </Typography>
              <Typography
                style={{ color: "gray", fontSize: "16pxRegistered Gateway" }}>
                {t("dashboard.esl_stores")}
              </Typography>
            </Grid>
            <Grid>
              <Typography variant="h2" fontWeight={"bold"}>
                {dashBoardData?.outletOfTotal}
              </Typography>
              <Typography style={{ color: "gray", fontSize: "16px" }}>
                {t("dashboard.total_stores")}
              </Typography>
            </Grid>
          </Grid>
          <Divider orientation="vertical" flexItem />
          <Grid
            item
            lg={6}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}>
            <Grid>
              <div
                style={{
                  position: "relative",
                  width: "160px",
                  height: "160px",
                }}>
                <PieSvg
                  value={
                    dashBoardData?.outletInstallPercentage
                      ? dashBoardData?.outletInstallPercentage
                      : 0
                  }></PieSvg>
              </div>
            </Grid>

            <Typography
              style={{ color: "gray", fontSize: "16px", marginTop: "20px" }}>
              {t("dashboard.stores_installed")}
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </React.Fragment>
  );
}

export default DeviceDistribution;
