import CloseIcon from "@mui/icons-material/Close";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "notistack";
import WarmingIcon from "@a/images/warming.svg?react";
import CustomInput from "@c/CustomInput";
import { batchEdit } from "@s/screens.js";
import CommonUtil from "@u/CommonUtils";

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
  "& .MuiDialog-paperFullWidth": {
    maxWidth: "1200px",
  },
}));

function BatchUpdateScreen(props) {
  const { open, setOpen, screenIds, loadData } = props;

  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();

  const [bindGateway, setBindGateway] = useState(null);

  const bindGatewayOptions = [
    {
      id: "0",
      value: t("common.is_not"),
    },
    {
      id: "1",
      value: t("common.is"),
    },
  ];

  const validateForm = () => {
    if (CommonUtil.isEmptyString(payload.sn)) {
      setError({
        ...error,
        sn: t("tips.required"),
      });
      return;
    }

    return true;
  };

  const [payload, setPayload] = useState({
    screenName: "",
    positionNo: "",
    sn: "",
  });

  const [error, setError] = useState({
    screenName: "",
    positionNo: "",
    sn: "",
  });

  const handlerSubmit = () => {
    if (validateForm()) {
      var request = {
        screenName: payload.screenName,
        positionNo: payload.positionNo,
        sn: payload.sn,
        bindDevice: bindGateway?.id,
        screenIds,
      };

      if (bindGateway?.id == null || bindGateway?.id == null) {
        enqueueSnackbar(t("screen.is_bindDevice"), {
          variant: "error",
        });
        return;
      }
      batchEdit(request).then((res) => {
        if (res?.data?.code === "LVLI0000") {
          enqueueSnackbar(t("tips_screens.batch_update_screen_success"), {
            variant: "success",
          });

          setOpen(false);
          loadData();
          setPayload({
            ...payload,
            screenName: "",
            positionNo: "",
            sn: "",
          });
          setBindGateway(null);
        } else {
          enqueueSnackbar(res?.data?.message || t("common_tips.operate_fail"), {
            variant: "error",
          });
        }
      });
    }
  };

  const handleChange = (event) => {
    const name = event.target.name;
    setPayload({
      ...payload,
      [name]: event.target.value,
    });

    setError({
      ...error,
      [name]: "",
      common: "",
    });
  };

  return (
    <React.Fragment>
      <BootstrapDialog
        onClose={() => setOpen(false)}
        aria-labelledby="customized-dialog-title"
        open={open}
        fullWidth>
        <DialogTitle
          sx={{
            m: 0,
            p: 2,
            color: `var(--unnamed-color-474b4f)`,
            font: `normal normal medium 16px/20px Roboto`,
            color: "#474B4F",
            opacity: 0.8,
          }}
          id="customized-dialog-title">
          {t("screen.batchUpdateScreen")}
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={() => setOpen(false)}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}>
          <CloseIcon />
        </IconButton>
        {/* <DialogContent dividers> */}
        <DialogContent>
          {/* <TextBox></TextBox> */}
          <Grid container spacing={2} px={2} mt={1}>
            <Grid item md={6} xs={12}>
              <CustomInput
                id="EditScreen5"
                required
                label={t("screen.gateway")}
                size="small"
                name="sn"
                error={error.sn}
                resetError={() => setError({ ...error, sn: "" })}
                value={payload.sn}
                handleChange={handleChange}
                inputProps={{
                  maxLength: 60,
                }}
                helperText={error.sn}
                placeholder={t("tips_screens.enter_gateway_sn")}
                validation={"alpha-numeric"}
                // disabled={true}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <InputLabel
                shrink
                htmlFor="bootstrap-input"
                style={{ paddingLeft: "0px" }}>
                {t("template.is_bind_device")}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>

              <Autocomplete
                id="AddTemplateModel"
                disableClearable
                options={bindGatewayOptions}
                value={bindGateway}
                getOptionLabel={(option) =>
                  option?.value ? option?.value : ""
                }
                name="bindDevice"
                onChange={(e, v) => {
                  setBindGateway(v);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    name="bindDevice"
                    size="small"
                    sx={{
                      "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall": {
                        fontSize: "13px",
                        padding: "12px",
                        height: "28px",
                        borderRadius: "18px",
                      },
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <CustomInput
                id="EditScreen2"
                label={t("screen.positionNo")}
                size="small"
                name="positionNo"
                error={error.positionNo}
                resetError={() => setError({ ...error, positionNo: "" })}
                value={payload.positionNo}
                handleChange={handleChange}
                inputProps={{
                  maxLength: 5,
                }}
                placeholder={t("tips_screens.enter_position_no")}
                helperText={error.positionNo}
                validation={"alpha-numeric"}
                // disabled={true}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <CustomInput
                id="EditScreen3"
                label={t("screen.screenName")}
                size="small"
                name="screenName"
                error={error.screenName}
                resetError={() => setError({ ...error, screenName: "" })}
                value={payload.screenName}
                handleChange={handleChange}
                inputProps={{
                  maxLength: 10,
                }}
                placeholder={t("tips_screens.enter_screen_name")}
                helperText={error.screenName}
                validation={"alpha-numeric"}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button
            autoFocus
            onClick={() => setOpen(false)}
            sx={{
              width: "165px",
              height: "64px",
              background: `var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box`,
              border: `1px solid var(--unnamed-color-1487ca)`,
              background: "#FFFFFF 0% 0% no-repeat padding-box",
              border: "1px solid #1487CA",
              borderRadius: "8px",
            }}>
            {t("common.cancel")}
          </Button>

          <Button
            autoFocus
            onClick={handlerSubmit}
            sx={{
              width: "165px",
              height: "64px",
              background: `transparent linear-gradient(270deg, var(--unnamed-color-1487ca) 0%, #78BC27 100%) 0% 0% no-repeat padding-box`,
              background: `transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box`,
              color: "#fff",
              borderRadius: "8px",
            }}>
            {t("common.confirm")}
          </Button>
        </DialogActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default BatchUpdateScreen;

//  文字盒子
const TextBox = () => {
  const textStyle = {
    textAlign: "left",
    font: `normal normal normal 14px/19px Roboto`,
    color: "#E67900",
    mt: 1,
  };
  return (
    <Grid
      sx={{
        background: ` #FDC48526 0% 0% no-repeat padding-box`,
        borderRadius: "8px",
        height: "118px",
        display: "flex",
        alignItems: "center",
      }}>
      <Grid ml={2}>
        <Grid container flexDirection={"row"}>
          <WarmingIcon />
          <Typography
            sx={{
              textAlign: "left",
              font: `normal normal medium 16px/19px Roboto`,
              color: "#E67900",
              ml: 2,
              mt: 1,
            }}>
            Helpful Tips
          </Typography>
        </Grid>
        <Grid ml={5.5}>
          <Typography sx={textStyle}>
            If the versions are the same, the one with the latest time will be
            prioritized!
          </Typography>
          <Typography sx={textStyle}>
            Please choose a version greater than the currently selected device
            version; Otherwise, the upgrade will not be possible: (Current
            versions of all devices:)
          </Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};
