import React from "react";
import { useTranslation } from "react-i18next";
import { handleDelete } from "./utils";
function Delete(props) {
  const { id, confirm, setConfirm } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Dialog open={confirm} onClose={() => setConfirm(false)}>
        <DialogTitle>
          <Typography variant="subtitle2">
            {t("tips.selected_delete_record")}
          </Typography>
        </DialogTitle>
        <Grid
          item
          xs={12}
          sm={12}
          md={12}
          lg={12}
          xl={12}
          padding={"10px"}
          justifyContent={"center"}>
          <Box display="flex" justifyContent="center">
            <Button
              variant="outlined"
              color="primary"
              style={{ fontSize: "normal normal normal 14px / 22px Roboto" }}
              onClick={() => setConfirm(false)}
              sx={{ marginRight: 2 }}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="contained"
              color="primary"
              style={{
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
                color: "#FFFFFF",
                fontSize: "normal normal normal 14px / 22px Roboto",
                width: "80px",
              }}
              onClick={() => handleDelete(id, setConfirm)}>
              {t("common.delete")}
            </Button>
          </Box>
        </Grid>
      </Dialog>
    </React.Fragment>
  );
}

export default Delete;
