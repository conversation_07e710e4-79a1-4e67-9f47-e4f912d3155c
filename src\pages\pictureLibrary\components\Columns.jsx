import IconHandaler from "@c/IconHandaler";
import SearchIcon from "@mui/icons-material/Search";
import DeleteIcon from "@a/images/Delete_Icon.svg?react";
import EditIcon from "@a/images/Edit_Icon.svg?react";
import AuthButton from "@c/AuthButton";

export const getColumns = (t, navigate, setOpen, setDeleteId) => {
  let columns = [
    {
      field: "pictureName",
      headerName: t("picture_library.name"),
      flex: 1,
      renderCell: (e) => e.row.pictureName,
    },
    {
      field: "pictureId",
      headerName: t("picture_library.id"),
      flex: 1,
      renderCell: (e) => {
        return (
          <>
            <Tooltip>
              <span>{e.row.pictureId}</span>
            </Tooltip>
          </>
        );
      },
    },
    {
      field: "url",
      headerName: t("picture_library.preview"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Avatar
            sx={{ bgcolor: "#C0C0C0", marginRight: "16px" }}
            imgProps={{ draggable: "false" }}
            alt={e.row.firstName}
            src={e.row.url}
            variant="circular"></Avatar>
        </>
      ),
    },
    {
      field: "pictureType",
      headerName: t("picture_library.type"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip>
          <span>
            {e.row.pictureType == "0"
              ? t("picture_library.general_user")
              : t("picture_library.company_logo")}
          </span>
        </Tooltip>
      ),
    },

    {
      field: "pictureProcessing",
      headerName: t("picture_library.progressing"),
      flex: 1.5,
      renderCell: (e) => (
        <Tooltip>
          <span>
            {e.row.pictureProcessing == "0"
              ? t("picture_library.original_image")
              : t("picture_library.dithering_image")}
          </span>
        </Tooltip>
      ),
    },
    {
      headerName: t("common.actions"),
      sortable: false,
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (e) => {
        return (
          <IconHandaler>
            <AuthButton button="nt:nutag:picture_library:update">
              <Tooltip title={t("common.edit")} arrow>
                <EditIcon
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                    padding: "2px",
                  }}
                  onClick={() => {
                    navigate("/add/picture-library", {
                      state: {
                        id: e.id,
                        type: "editor",
                      },
                    });
                  }}
                />
              </Tooltip>
            </AuthButton>
            <AuthButton button="nt:nutag:picture_library:delete">
              <Tooltip title={t("common.delete")} arrow sx={{ marginLeft: 1 }}>
                <DeleteIcon
                  style={{
                    alignSelf: "center",
                    paddingTop: "0px",
                    cursor: "pointer",
                    opacity: "0.6",
                    height: "17px",
                    width: "20px",
                    padding: "2px",
                  }}
                  onClick={() => {
                    setOpen(true);
                    setDeleteId(e.row.id);
                  }}
                />
              </Tooltip>
            </AuthButton>
          </IconHandaler>
        );
      },
    },
  ];

  return columns;
};
