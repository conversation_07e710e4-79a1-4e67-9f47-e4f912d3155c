import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Grid,
  TextField,
  Typography,
} from "@mui/material";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import CommonUtil from "../../../util/CommonUtils";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { filterOutlet } from "../../../services/OutletService";

export default function AreaFilter(props) {
  const { t } = useTranslation();
  const [outlets, setOutlets] = useState([]);

  const {
    payload,
    setPayload,
    outletIds,
    setOutlet,
    outlet,
    isAreaExpanded,
    handleToggleAreaAccordion,
    handleToggleProductAccordion,
  } = props;

  const [filteredOutlets, setFilteredOutlets] = useState([]);
  const [timezone, setTimezone] = useState("");

  useEffect(() => {
    if (outletIds && outlets) {
      const filteredOutlets = outlets.filter((outlet) =>
        outletIds?.includes(outlet?.id)
      );
      setFilteredOutlets(filteredOutlets);
    }
  }, [outletIds, outlets]);

  const [outletFilter, setOutletFilter] = useState({
    country: "",
    city: "",
    state: "",
  });

  const loadDataOutlets = (outletFilter) => {
    filterOutlet(1, 1000, outletFilter).then((response) => {
      if (response?.data?.code === "LMSI6000") {
        setOutlets(response?.data?.data?.objects);
      } else {
        setOutlets([]);
      }
      if (response?.data?.code === "LVLE0054") {
        setOutlets([]);
      }
    });
  };

  useEffect(() => {
    loadDataOutlets(outletFilter);
  }, [outletFilter]);

  const handleAreaCancel = () => {
    setOutlet({});
  };

  return (
    <Accordion
      elevation={0}
      style={{
        width: "auto",
        height: "auto",
        background: "#FFFFFF 0% 0% no-repeat padding-box",
        borderRadius: "8px",
        opacity: "1",
      }}
      expanded={isAreaExpanded}
      onChange={handleToggleAreaAccordion}
    >
      <AccordionSummary
        expandIcon={<GridExpandMoreIcon />}
        aria-controls="panel1-content"
        id="panel1-header"
        sx={{ fontFamily: "Roboto" }}
      >
        {t("events.areaFilter")}
      </AccordionSummary>
      <AccordionDetails>
        <Grid container spacing={1}>
          <Grid item lg={4}>
            <Typography
              sx={{
                fontSize: "12px",
                color: "#474B4F",
                opacity: "0.8",
                paddingBottom: "8px",
              }}
            >
              {t("PITE0005")}
              <span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              noOptionsText={t("LVLGF0011")}
              options={filteredOutlets}
              value={outlet}
              onChange={(e, v) => {
                setOutlet(v);
                setPayload({
                  ...payload,
                  outletId: v?.id,
                  outletName: v?.name,
                });
                setTimezone(
                  v ? CommonUtil.getTimeZoneByValue(v?.address?.timeZone) : ""
                );
              }}
              getOptionLabel={(option) => (option?.name ? option?.name : "")}
              renderInput={(params) => <TextField {...params} size="small" />}
            />
          </Grid>
        </Grid>
      </AccordionDetails>
      <Grid container spacing={2} pb={2} pr={2}>
        <Grid item xs={12}>
          <Box display={"flex"} flexDirection={"row-reverse"}>
            <Box item pl={2}>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="contained"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={handleToggleProductAccordion}
              >
                {t("common.next")}
              </Button>
            </Box>
            <Box item>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="none"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  color: "#1487CA",
                }}
                onClick={handleAreaCancel}
              >
                {t("common.previous")}
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Accordion>
  );
}
