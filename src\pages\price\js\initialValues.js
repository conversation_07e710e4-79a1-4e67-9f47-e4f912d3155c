import { numberToDate } from "./way";

export const getInitialValues = (state, detailData) => {
  if (state?.action == "Edit" && detailData) {
    return {
      name: detailData?.name,
      promotionType: detailData?.promotionType,
      promotionValue: detailData?.promotionValue,
      scheduleMode:
        detailData?.scheduleMode == "1" ? "" : detailData?.scheduleMode,
      outletIds: detailData?.outletIds,
      productId: detailData?.productId,
      templateId: detailData?.templateId,
      startAt: detailData?.startAt, // Start Promotion
      endAt: detailData?.endAt, //End Promotion
      startDayTime: detailData?.startDayTime, // Start Time
      endDayTime: detailData?.endDayTime, //End Time
      fullDay: detailData?.fullDay,
      days: detailData.days || [],
      dates: numberToDate(detailData.dates) || [],
      months: detailData?.months || [],
      years: detailData?.years || [],
      id: state?.id,
      resolution: detailData?.resolution,
      timeZone: detailData.timeZone,
      url: detailData.url,
      endOrNot: detailData?.endOrNot,
      isAllowEdit: detailData?.isAllowEdit,
      productAttributeId: detailData?.productAttributeId,
      productAttributeValue: detailData?.productAttributeValue,
      productName: detailData?.productName,
    };
  } else {
    return {
      name: "",
      promotionType: "",
      promotionValue: "",
      scheduleMode: "",
      outletIds: [],
      productId: "",
      templateId: "",
      startDayDate: "", // Start Promotion
      endDayDate: "", //End Promotion
      startDayTime: "", // Start Time
      endDayTime: "", //End Time
      fullDay: false,
      endOrNot: false,
      days: [],
      dates: [],
      months: [],
      years: [],
      resolution: "",
      timeZone: "",
      status: "0",
      url: "",
      productAttributeId: "",
      productAttributeValue: "",
      timeDay: "",
      endTime: "",
      endTimeDay: "",
      startTime: "",
    };
  }
};
