const getResolutionByIdentifier = (identifier) => {

    const digits = identifier.substring(1, 3).toLowerCase();

    switch (digits) {
      case "40":
        return "200*200";
      case "41":
        return "200*200";
      case "42":
        return "202*104";
      case "43":
        return "250*128";
      case "44":
        return "296*128";
      case "45":
        return "296*128";
      case "46":
        return "296*128";
      case "47":
        return "400*300";
      case "48":
        return "640*384";
      case "49":
        return "640*384";
      case "4a":
        return "800*480";
      case "4b":
        return "800*480";
      case "4c":
        return "296*152";
      case "4d":
        return "296*152";
      case "4e":
        return "360*240";
      case "4f":
        return "360*240";
      case "50":
        return "648*480";
      case "51":
        return "648*480";
      case "52":
        return "416*240";
      case "53":
        return "416*240";
      case "54":
        return "250*128";
      case "55":
        return "960*640";
      case "56":
        return "960*640";
      case "70":
        return "200*200";
      case "71":
        return "250*128";
      case "72":
        return "296*152";
      case "73":
        return "296*128";
      case "74":
        return "360*240";
      case "75":
        return "400*300";
      case "76":
        return "648*480";
      case "77":
        return "640*384";
      default:
        return null;
    }
  };
  export default getResolutionByIdentifier;

