import { Grid } from "@mui/material";
import { useEffect, useState, useRef } from "react";
import { FormLabel, PrettoSlider } from "../components/PropertiesComponent";
import CustomInput from "../components/CustomInput";
import { Stack, InputLabel, OutlinedInput } from "@mui/material";
import { useTranslation } from "react-i18next";
const ImagePropertise = (props) => {
  const {
    setActiveId,
    activeId,
    current,
    setCurrent,
    layoutJSON,
    setLayoutJSON,
  } = props;
  const { t } = useTranslation();
  const [properties, setProperties] = useState({
    type: "",
    id: "",
    left: 0,
    top: 0,
    right: 0,
    width: 200,
    height: 100,
    hide: false,
    zIndex: 10,
    rotate: 0,
    url: "",
    transparency: 1, //透明度
    borderRadius: 0,
  });

  useEffect(() => {
    let componentList = JSON.parse(JSON.stringify(layoutJSON.componentList));
    let componentListResult = componentList.filter((element, fIndex) => {
      if (element.id === activeId) {
        return true;
      } else {
        return false;
      }
    });
    if (componentListResult.length === 1) {
      let aa = componentListResult[0];
      setProperties({
        ...properties,
        ...aa,
      });
    }
  }, [activeId, current, layoutJSON]);

  const updateProperties = (newInfo) => {
    let jsonObj = JSON.parse(JSON.stringify(layoutJSON));
    setProperties(newInfo);
    let index = "";
    jsonObj.componentList.forEach((element, fIndex) => {
      if (element.id === newInfo.id) {
        index = fIndex;
      }
    });
    if (index !== "") {
      jsonObj.componentList[index] = newInfo;
      setLayoutJSON(JSON.parse(JSON.stringify(jsonObj)));
    }
  };

  const changeProperties = (event) => {
    const name = event.target.name;
    let newInfo = {
      ...properties,
      [name]: event.target.value,
    };
    updateProperties(newInfo);
  };

  const handleSliderRadiusChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      borderRadius: event.target.value,
    };
    updateProperties(newInfo);
  };

  const handleRotationChange = (event, newValue) => {
    let left = properties.left;
    let top = properties.top;
    let width = properties.width;
    let height = properties.height;

    // 固定旋转中心点，假设初始化时设置
    const centerX = left + width / 2;
    const centerY = top + height / 2; // 固定的旋转中心 Y

    // 计算旋转角度的弧度
    const angle = (newValue * Math.PI) / 180;

    // 计算旋转后的矩形四个顶点的坐标
    const points = [
      { x: left, y: top }, // 左上
      { x: left + width, y: top }, // 右上
      { x: left, y: top + height }, // 左下
      { x: left + width, y: top + height }, // 右下
    ];

    // 使用旋转矩阵计算每个顶点的新位置
    const rotatedPoints = points.map((point) => {
      const x = point.x;
      const y = point.y;

      // 旋转公式
      const rotatedX =
        (x - centerX) * Math.cos(angle) -
        (y - centerY) * Math.sin(angle) +
        centerX;
      const rotatedY =
        (x - centerX) * Math.sin(angle) +
        (y - centerY) * Math.cos(angle) +
        centerY;

      return { x: rotatedX, y: rotatedY };
    });

    // 计算旋转后的矩形的最小和最大X、Y坐标（即新的边界）
    const minX = Math.min(...rotatedPoints.map((p) => p.x));
    const maxX = Math.max(...rotatedPoints.map((p) => p.x));
    const minY = Math.min(...rotatedPoints.map((p) => p.y));
    const maxY = Math.max(...rotatedPoints.map((p) => p.y));

    // 计算旋转后矩形的新的 left, top, width, height
    const newLeft = minX;
    const newTop = minY;
    const newWidth = maxX - minX;
    const newHeight = maxY - minY;

    // 调整 left 和 top，确保矩形旋转后的左上角位置正确
    const correctedLeft = newLeft + width / 2 - newWidth / 2;
    const correctedTop = newTop + height / 2 - newHeight / 2;

    // 更新矩形的旋转角度和新的属性
    const newInfo = {
      ...properties,
      // left: Math.floor(correctedLeft),
      // top: Math.floor(correctedTop),
      rotate: newValue,
    };

    updateProperties(newInfo);
  };
  const handleSliderChange = (event, newValue) => {
    let newInfo = {
      ...properties,
      transparency: event.target.value,
    };
    updateProperties(newInfo);
  };

  return (
    <Grid
      sx={{
        height: "100%",
        overflow: "auto",
        p: 1.5,
      }}>
      <Grid
        sx={{
          display: "flex",
          alignItems: "center",
          mt: 2,
        }}>
        <FormLabel sx={{ mr: 2 }}>{t("editor.rounded_corners")}</FormLabel>
        <PrettoSlider
          onChange={handleSliderRadiusChange}
          size="small"
          min={0}
          max={500}
          step={1}
          color="secondary"
          value={properties.borderRadius}
          aria-label="Small"
          valueLabelDisplay="on"></PrettoSlider>
      </Grid>

      <Grid
        sx={{
          display: "flex",
          alignItems: "center",
          mt: 3,
        }}>
        <FormLabel sx={{ mr: 2 }}>{t("editor.rotation_angle")}</FormLabel>
        <PrettoSlider
          onChange={handleRotationChange}
          size="small"
          min={0}
          max={360}
          step={1}
          color="secondary"
          value={properties.rotate}
          aria-label="Small"
          valueLabelDisplay="on"></PrettoSlider>
      </Grid>

      <Grid
        sx={{
          display: "flex",
          alignItems: "center",
          mt: 3,
        }}>
        <FormLabel sx={{ mr: 2 }}>{t("editor.opacity")}</FormLabel>
        <PrettoSlider
          onChange={handleSliderChange}
          size="small"
          min={0.1}
          max={1}
          step={0.1}
          color="secondary"
          value={properties.transparency}
          aria-label="Small"
          valueLabelDisplay="on"></PrettoSlider>
      </Grid>

      <Grid
        sx={{
          mt: 2,
        }}>
        <InputLabel
          sx={{
            color: "#707070",
            fontSize: "14px",
            mr: 2,
            whiteSpace: "nowrap",
            flexShrink: 0,
          }}>
          {t("editor.level")}
        </InputLabel>
        <CustomInput
          label=""
          type="number"
          value={properties.zIndex}
          onChange={changeProperties}
          name="zIndex"></CustomInput>
      </Grid>

      <Grid sx={{ mt: 2 }}>
        <CustomInput
          label="X："
          value={properties.left}
          onChange={changeProperties}
          name="left"></CustomInput>

        <CustomInput
          label="Y："
          value={properties.top}
          onChange={changeProperties}
          name="top"></CustomInput>

        <CustomInput
          label={t("editor.width")}
          value={properties.width}
          onChange={changeProperties}
          name="width"></CustomInput>

        <CustomInput
          label={t("editor.height")}
          value={properties.height}
          onChange={changeProperties}
          name="height"></CustomInput>
      </Grid>
    </Grid>
  );
};

export default ImagePropertise;
