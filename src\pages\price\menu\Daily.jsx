import { Box, Grid, InputLabel, FormControlLabel, Radio } from "@mui/material";
import { useTranslation } from "react-i18next";
import TimePickerDate from "@c/TimePickerDate";
import { useEffect, useState } from "react";
import dayjs from "dayjs";

function Daily(props) {
  const { addFormik } = props;
  const { t } = useTranslation();

  const [allDay, setAllday] = useState(false);

  useEffect(() => {
    setAllday(addFormik.values.fullDay);
  }, [addFormik.values.fullDay]);

  useEffect(() => {
    if (allDay) {
      addFormik.setFieldValue(
        "startAt",
        dayjs().startOf("day").format("HH:mm:ss")
      );

      addFormik.setFieldValue("endAt", dayjs().endOf("day").format("HH:mm:ss"));
    }
  }, [allDay]);

  return (
    <Grid display={"flex"} pb={3}>
      <Box m={1}>
        <Grid mt={2}>
          <TimePickerDate
            name="startAt"
            disabled={allDay}
            label={t("common.start_time")}
            required
            defaultValue={dayjs(addFormik.values.startAt, "HH:mm:ss")}
            value={
              allDay
                ? dayjs().startOf("day")
                : dayjs(addFormik.values.startAt, "HH:mm:ss")
            }
            fontSize={"26px"}
            onChange={(e) => {
              addFormik?.setFieldValue("startAt", dayjs(e).format("HH:mm:ss"));
            }}
            placeholder={t("common_tips.start_date")}></TimePickerDate>
        </Grid>
      </Box>

      <Box m={1}>
        <Grid mt={2}>
          <TimePickerDate
            name="endAt"
            label={t("common.end_time")}
            disabled={allDay}
            fontSize={"26px"}
            required
            defaultValue={dayjs().endOf("day").format("HH:mm:ss")}
            placeholder={t("common_tips.end_date")}
            value={
              allDay
                ? dayjs().endOf("day")
                : dayjs(addFormik.values.endAt, "HH:mm:ss")
            }
            onChange={(e) => {
              addFormik?.setFieldValue("endAt", dayjs(e).format("HH:mm:ss"));
            }}></TimePickerDate>
        </Grid>
      </Box>

      <Box mt={4} ml={5}>
        <FormControlLabel
          control={
            <Radio
              id={`role-radio-1`}
              checked={allDay}
              value={allDay}
              onClick={() => {
                const newValue = !allDay; // 切换值
                setAllday(newValue); // 更新状态
                addFormik.setFieldValue("fullDay", newValue); // 更新 Formik 的值
              }}
              name="AllDay"
            />
          }
          label={t("events.allDay")}
        />
      </Box>
    </Grid>
  );
}

export default Daily;
