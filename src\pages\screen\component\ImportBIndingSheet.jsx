import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>p<PERSON>onte<PERSON>,
  BootstrapDialogT<PERSON><PERSON>,
  BootstrapDialog,
} from "@/components/dialog/index.js";
import { Typography, Button, Box, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import GroupIcon from "@/assets/Icons/Group 25808.svg?react";
import ArrowCircleDownIcon from "@mui/icons-material/ArrowCircleDown"; // 添加这个导入
import {
  getDownloadExcel,
  importScreen,
  Importprogress,
} from "@/services/screens";
import { toast } from "react-toastify";
import { LinearProgress } from "@mui/material"; // {
function ImportBIndingSheet(props) {
  const { open, onClose } = props;
  const { t } = useTranslation();
  const [selectedFile, setSelectedFile] = useState(null);
  const [progress, setProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const handleImportAndRefresh = async () => {
    try {
      setIsImporting(true);
      setProgress(0);
      const response = await importScreen({ file: selectedFile });
      setTaskId(response.data);
      // 导入开始后，进度更新将由 useEffect 处理
    } catch (error) {
      setIsImporting(false);
      toast.error("Import failed");
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await getDownloadExcel();

      // 创建一个Blob对象
      const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "binding_template.xlsx");

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Failed to download template");
    }
  };

  useEffect(() => {
    let intervalId;
    if (isImporting && taskId) {
      intervalId = setInterval(async () => {
        try {
          const progressData = await Importprogress(taskId);

          let processed = progressData?.data?.processed;
          const percent = progressData?.data?.percent || 0; // 百分比

          setProgress(percent);
          if (percent >= 100) {
            setIsImporting(false);
            clearInterval(intervalId);
            toast.success("Import completed successfully");
          }
        } catch (error) {
          console.error("Failed to fetch progress:", error);
        }
      }, 1000); // 每秒更新一次进度
    }
    return () => clearInterval(intervalId);
  }, [isImporting, taskId]);

  return (
    <React.Fragment>
      <BootstrapDialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={onClose}
        aria-labelledby="customized-dialog-title">
        <BootstrapDialogTitle>
          <Typography
            style={{
              font: `normal normal normal 16px/18px Roboto`,
              color: `#000`,
            }}>
            Import Binding Sheet
          </Typography>
        </BootstrapDialogTitle>

        <BootstrapContent>
          <Grid
            sx={{
              mb: 3,
              height: "90px",
              background: `#f3f9fc 0% 0% no-repeat padding-box`,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              pl: 2,
              pr: 2,
            }}>
            <Typography
              sx={{
                font: `normal normal normal 16px/20px Roboto`,
                color: "#474B4F",
                opacity: 0.8,
              }}>
              Click here to download the sample file
            </Typography>
            <Button
              variant="outlined"
              sx={{
                color: "#1976d2",
                border: `1px solid #1487CA`,
                width: "212px",
                height: "50px",
              }}
              startIcon={<ArrowCircleDownIcon />} // 添加圆形向下箭头图标
              onClick={handleDownloadTemplate}>
              Download Template
            </Button>
          </Grid>

          <Box
            sx={{
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              p: 4,
              textAlign: "center",
              backgroundColor: "#fafafa",
              minHeight: "200px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
            }}>
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileSelect}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                opacity: 0,
                cursor: "pointer",
              }}
            />

            <GroupIcon
              style={{ width: "80px", height: "80px", marginBottom: "16px" }}
            />

            {(progress > 0 || isImporting) && (
              <Box sx={{ width: "100%", mt: 2 }}>
                <LinearProgress variant="determinate" value={progress} />
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center">
                  {`${Math.round(progress)}%`}
                </Typography>
              </Box>
            )}

            {progress > 0 ? null : (
              <Box>
                <Typography
                  sx={{
                    color: "#1976d2",
                    fontSize: "16px",
                    fontWeight: 500,
                    mb: 2,
                  }}>
                  Choose a file to import
                </Typography>

                <Typography
                  variant="body2"
                  sx={{ color: "#666", fontSize: "12px", mb: 1 }}>
                  <strong>Import:</strong> Import only binding relationships.
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "#666", fontSize: "12px" }}>
                  <strong>Refresh:</strong> Import binding relationships and
                  refresh.
                </Typography>
              </Box>
            )}

            {selectedFile && (
              <Typography
                sx={{
                  mt: 2,
                  color: "#4caf50",
                  fontSize: "14px",
                }}>
                {selectedFile.name}
              </Typography>
            )}
          </Box>
        </BootstrapContent>

        <BootstrapActions>
          <Button
            variant="contained"
            onClick={handleImportAndRefresh}
            disabled={!selectedFile || isImporting}
            sx={{
              width: "180px",
              height: "64px",
              background: `transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box`,
              font: `normal normal normal 16px/18px Roboto`,
              color: "#FFFFFF",
            }}>
            {isImporting ? "Importing..." : "Import and Refresh"}
          </Button>
        </BootstrapActions>
      </BootstrapDialog>
    </React.Fragment>
  );
}

export default ImportBIndingSheet;
