import { Form<PERSON>elperText, InputLabel, Stack } from "@mui/material";
import React from "react";
import RequirePoint from "../RequirePoint";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";

const ZkRadio = (props) => {
  const {
    formik = null,
    placeholder = "",
    labeloptions = { label: "label", value: "value" },
    handleBlur,
    handleChange,
    label,
    dicType = "",
    isDic = false,
    name,
    error,
    value,
    labelpostion,
    options = [],
    ...orther
  } = props;

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleBlur) {
      handleBlur(e);
    }
  };

  const [selectOption, setSelectOptions] = useState([]);

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
            }}
            htmlFor={"zkSelect" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}
        <Stack
          sx={{
            width: "100%",
            marginTop: "1px !important",
            maxWidth:
              label && labelpostion == "left" ? "calc(100% - 60px )" : "100%",
          }}>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            defaultValue={value}
            row
            name={name}
            value={formik ? formik.values[name] : value}
            onChange={changeFn}
            sx={{
              ".MuiFormControlLabel-label": {
                color: "#474b4fcc",
              },
            }}
            {...orther}>
            {selectOption.map((item) => {
              return (
                <FormControlLabel
                  key={isDic ? item.value : item[labeloptions.value]}
                  value={isDic ? item.value : item[labeloptions.value]}
                  control={<Radio />}
                  label={isDic ? item.text : item[labeloptions.label]}
                />
              );
            })}
          </RadioGroup>
          {((formik?.touched[name] && formik?.errors[name]) || error) && (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {formik?.errors[name] || error}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};
export default ZkRadio;
