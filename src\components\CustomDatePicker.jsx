import React, { useEffect, useState } from "react";
import { Grid, styled } from "@mui/material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DesktopDatePicker } from "@mui/x-date-pickers/DesktopDatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CalenderIcon from "@/assets/images/calendar-icon.svg?react";

import CustomInput from "./CustomInput";

const CustomIconLeft = styled(ChevronLeftIcon)(() => ({
  border: "1px solid green",
  borderRadius: "50%",
  "&:hover": {
    backgroundColor: "green",
    color: "#FFFF",
  },
}));

const CustomIconRight = styled(ChevronRightIcon)(() => ({
  border: "1px solid green",
  borderRadius: "50%",
  "&:hover": {
    backgroundColor: "green",
    color: "#FFFF",
  },
}));

export default function CustomDatePicker(props) {
  const [date, setDate] = useState(null);

  useEffect(() => {
    setDate(props.date);
  }, [props.date]);

  const join = (t, a, s) => {
    try {
      function format(m) {
        let f = new Intl.DateTimeFormat("en", m);
        return f.format(t);
      }
      return a.map(format).join(s);
    } catch (error) {}
  };

  const handleDate = (e) => {
    let formet = [{ day: "numeric" }, { month: "short" }, { year: "numeric" }];
    let formetChange = join(e, formet, " ");
    setDate(e);
    props.SelectedDate(e);
  };

  return (
    <Grid style={{ width: "100%", height: "100%" }}>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DesktopDatePicker
          inputFormat={"dd-MM-yyyy"}
          disableFuture={props.disableFuture}
          disabled={props.disabled}
          placement="bottom-right"
          components={{
            OpenPickerIcon: CalenderIcon,
            LeftArrowIcon: CustomIconLeft,
            RightArrowIcon: CustomIconRight,
          }}
          label={props.label}
          dateRangeIcon={<CalenderIcon />}
          views={["year", "month", "day"]}
          value={date}
          sx={{
            p: 1,
            border: "1px solid black",
          }}
          onChange={(e) => handleDate(e)}
          renderInput={(params) => (
            <CustomInput
              fullWidth={true}
              handleChange={(e) => console.log()}
              size={props.size ? props.size : " "}
              value={date}
              required={props.required}
              style={{ width: "100%" }}
              {...params}
              inputProps={{
                ...params.inputProps,
                placeholder: props.placeholder,
              }}
            />
          )}
        />
      </LocalizationProvider>
    </Grid>
  );
}
