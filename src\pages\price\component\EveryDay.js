import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Grid,
  Typography,
  FormControlLabel,
  Radio,
  FormGroup,
} from "@mui/material";
import dayjs from "dayjs";
import { generateDaysOfWeek } from "../js/way";
import { parseNonNullablePickerDate } from "@mui/x-date-pickers/internals";
function EveryDay(props) {
  const { selectedDays, setSelectedDays, endDayDate, startDayDate } = props;
  const [everyDay, setEveryDay] = useState(false);
  const weekdays = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  useEffect(() => {
    if (selectedDays.length === 7) {
      setEveryDay(true);
    } else {
      setEveryDay(false);
    }
  }, [selectedDays]);

  const handleDayChange = (weekday) => {
    setSelectedDays((prevSelectedWeekdays) => {
      if (prevSelectedWeekdays.includes(weekday)) {
        return prevSelectedWeekdays.filter((day) => day !== weekday);
      } else {
        return [...prevSelectedWeekdays, weekday];
      }
    });
  };

  const [disableDay, setDisableDay] = useState(null);
  useEffect(() => {
    //判断开始时间到结束时间一共多少天
    const totalDays = dayjs(endDayDate).diff(dayjs(startDayDate), "day") + 1;

    if (totalDays < 7) {
      setSelectedDays([]);
      const daysOfWeek = generateDaysOfWeek(startDayDate, endDayDate);
      setDisableDay(daysOfWeek);
    } else {
      setDisableDay(null);
    }
  }, [endDayDate, startDayDate]);
  const handleAllDays = (e) => {
    if (everyDay === false) {
      setEveryDay(true);
      setSelectedDays(weekdays ? weekdays : []);
    } else {
      setEveryDay(false);
      setSelectedDays(weekdays ? [] : weekdays);
    }
  };

  return (
    <Grid pt={2} lineHeight={3}>
      <Typography ml={3}>Include/Exclude days of the week</Typography>
      <Grid padding={3} container>
        <FormGroup row>
          {weekdays.map((value, index) => (
            <Grid key={index} item xs={1.5} pr={3}>
              <Button
                variant="contained"
                disabled={
                  disableDay !== null
                    ? disableDay.every((day) => day.dayOfWeekString !== value)
                    : false
                }
                color={selectedDays.includes(value) ? "success" : "inherit"}
                onClick={() => handleDayChange(value)}
                style={{
                  width: "50px",
                  height: "50px",
                  borderRadius: "100%",
                  background: selectedDays.includes(value)
                    ? "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box"
                    : "white",
                }}>
                {value.charAt(0)}
              </Button>
            </Grid>
          ))}
        </FormGroup>

        <Grid>
          <FormControlLabel
            style={{ paddingTop: 10 }}
            control={
              <Radio
                id={`role-radio-1}`}
                checked={everyDay}
                onClick={handleAllDays}
                value={everyDay}
                name="selection"
              />
            }
            label={"Everyday"}
          />
        </Grid>
      </Grid>
    </Grid>
  );
}

export default EveryDay;
