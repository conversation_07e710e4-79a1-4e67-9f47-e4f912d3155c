import i18n from "i18next";
import Backend from "i18next-xhr-backend";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import enUsTrans from "./modules/en";
import zhCnTrans from "./modules/zh";
import thGerman from "./modules/th";
import { getStoreLang } from "@/util/langUtils";

const initialLanguage = getStoreLang() || "en";
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: initialLanguage,
    initImmediate: false,
    fallbackLng: "en",
    preload: ["en", "es", "zh", "ge"],
    // preload: ["en", "zh"],
    debug: false,
    logger: {
      level: "error",
    },
    // backend: {
    // loadPath: "/assets/i18n/{{ns}}/{{lng}}.json",
    // loadPath: "/src/lang/{{ns}}/{{lng}}.js",
    // },

    // ns: ["translations"],
    // defaultNS: "translations",
    // ns: ["modules"],
    // defaultNS: "modules",
    resources: {
      en: {
        // 这里是我们的翻译文本
        translation: enUsTrans,
      },
      zh: {
        translation: zhCnTrans,
      },
      th: {
        translation: thGerman,
      },
    },

    keySeparator: ".",
    interpolation: {
      escapeValue: false,
      formatSeparator: ",",
    },
    react: {
      //   wait: true,
      bindI18n: "languageChanged loaded",
      bindStore: "added removed",
      nsMode: "default",
      useSuspense: true,
    },
  });

export default i18n;
