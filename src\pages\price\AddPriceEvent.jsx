import { useEffect, useState, createContext } from "react";
import { Grid } from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import RighViewLayout from "@c/RighViewLayout";
import dayjs from "dayjs";
import { useFormik } from "formik";
import { createValidation } from "@c/Config/validationUtils";
import { useSnackbar } from "notistack";
import { getInitialValues } from "./js/initialValues";
import { addChangeEvent, editChangeEvent } from "@s/price";
import {
  handlerUpdataFile,
  getPriceDetail,
  validateForm,
} from "./js/interface";
import PriceName from "./menu/PriceName";
import AreaOutlet from "./menu/AreaOutlet";
import ProductFilter from "./menu/ProductFilter";
import PriceRule from "./menu/PriceRule";
import Template from "./menu/Template";
import Schedule from "./menu/Schedule";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
export const ChangePriceContext = createContext();
function AddPriceEvent(props) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const { state } = useLocation();
  const { enqueueSnackbar } = useSnackbar();
  const [priceNameConfig, setPriceNameConfig] = useState([]); // 设备数量控制
  const [outletConfig, setOutletConfig] = useState([]); //门店列表数据
  const [productConfig, setProductConfig] = useState([]); //商品列表数据
  const [priceRuleConfig, setPriceRulrConfig] = useState([]); //变价规则模块配置数据
  const [templateConfig, setTemplateConfig] = useState([]); //模板选择模块配置数据
  const [scheduleConfig, setScheduleConfig] = useState([]); // schedule 变价计划模块配置数据
  const [expandedIndex, setExpandedIndex] = useState(false); // 是否打开当前模块

  const [productData, setProductData] = useState([]);
  const [templateJSON, setTemplateJSON] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [resolution, setResolution] = useState(""); // 报错模板分辨率
  const [editorTemplateJSON, setEditorTemplateJSON] = useState(null); // 回显模板JSON (仅回显使用)
  const formConfig = [
    ...priceNameConfig,
    ...outletConfig,
    ...productConfig,
    ...priceRuleConfig,
    ...templateConfig,
    ...scheduleConfig,
  ];

  const addFormik = useFormik({
    initialValues: getInitialValues(state, detailData),
    validationSchema: createValidation(formConfig),
    onSubmit: async (values) => {
      let updatedValues;

      const startTimeDay = dayjs(values.timeDay, "HH:mm:ss").format("HH:mm:00");

      if (values.endOrNot) {
        updatedValues = {
          ...values,
          startDayTime: `${values.startTime} ${startTimeDay}`,
          endDayTime: null,
        };
      } else {
        const endTimeDay = dayjs(values.endTimeDay, "HH:mm:ss").format(
          "HH:mm:00"
        );
        updatedValues = {
          ...values,
          startDayTime: `${values.startTime} ${startTimeDay}`,
          endDayTime: `${values.endTime} ${endTimeDay}`,
        };
      }
      if (updatedValues.dates) {
        updatedValues.dates = updatedValues.dates.map((x) =>
          Number.parseInt(x)
        );
      }

      if (validateForm(updatedValues)) {
        if (state?.action == "Edit") {
          // if (!detailData?.isAllowEdit) {
          //   toast.error(
          //     t("Price change event is being executed. Modification is forbidden")
          //   );
          //   return;
          // }

          if (templateJSON) {
            let res111 = await handlerUpdataFile(templateJSON, addFormik);

            const res = await editChangeEvent(state?.id, {
              ...updatedValues,
              id: state?.id,
              url: res111?.imageUrl,
              outletIds: values.outletIds
                ? values.outletIds.map((item) => (item.id ? item.id : item))
                : [],
              resolution: values.resolution ? values?.resolution : resolution,
              days: values.days ? values.days : [],
              dates: updatedValues.dates ? updatedValues.dates : [],
              months: values.months ? values.months : [],
              years: values.years ? values.years : [],
              fullDay: values.fullDay == true ? "1" : "0",
              endOrNot: values.endOrNot == true ? "1" : "0",
            });
            if (res?.code == "00000000") {
              toast.success(res?.message);
              navigate("/price-change-event");
            }
          }
        } else {
          if (templateJSON) {
            let data = await handlerUpdataFile(templateJSON, addFormik);
            const res = await addChangeEvent({
              ...updatedValues,
              url: data?.imageUrl,
              outletIds: values.outletIds
                ? values.outletIds.map((item) => (item.id ? item.id : item))
                : [],
              days: values.days ? values.days : [],
              dates: updatedValues.dates ? updatedValues.dates : [],
              months: values.months ? values.months : [],
              years: values.years ? values.years : [],
              fullDay: values.fullDay == true ? "1" : "0",
              endOrNot: values.endOrNot == true ? "1" : "0",
            });
            if (res?.code == "00000000") {
              toast.success(res?.message);
              navigate("/price-change-event");
            }
          }
        }
      }
    },
  });

  // 获取变价模块详情信息  （编辑回显用）
  useEffect(() => {
    if (state?.action == "Edit") {
      getPriceDetail(state?.id, setDetailData, addFormik);
    }
  }, [state?.id]);

  // 分解开始 促销变价时间和 结束促销变价时间
  useEffect(() => {
    if (!detailData) return;

    addFormik.setFieldValue("name", detailData?.name);
    addFormik.setFieldValue("timeZone", detailData?.timeZone);
    addFormik.setFieldValue("productId", detailData?.productId);
    addFormik.setFieldValue("templateId", detailData?.templateId);
    addFormik.setFieldValue("productName", detailData?.productName);
    addFormik.setFieldValue("templateName", detailData?.templateName);
    addFormik.setFieldValue("outletIds", detailData?.outletIds);
    addFormik.setFieldValue(
      "productAttributeId",
      detailData?.productAttributeId
    );
    addFormik.setFieldValue(
      "productAttributeValue",
      detailData?.productAttributeValue
    );
    addFormik.setFieldValue("promotionType", detailData?.promotionType);
    addFormik.setFieldValue("promotionValue", detailData?.promotionValue);
    addFormik.setFieldValue("scheduleMode", detailData?.scheduleMode);
    addFormik.setFieldValue("templateType", detailData?.templateType);
    addFormik.setFieldValue("startAt", detailData?.startAt);
    addFormik.setFieldValue("endAt", detailData?.endAt);
    addFormik.setFieldValue("fullDay", detailData?.fullDay);
    addFormik.setFieldValue("endOrNot", detailData?.endOrNot);

    addFormik.setFieldValue("startDayTime", detailData?.startDayTime);
    addFormik.setFieldValue("endDayTime", detailData?.endDayTime);
    addFormik.setFieldValue("years", detailData?.years);

    addFormik.setFieldValue("months", detailData?.months);
    addFormik.setFieldValue("days", detailData?.days);
    addFormik.setFieldValue("dates", detailData?.dates);

    // 更新组件状态
    setResolution(detailData?.templateVO?.resolution ?? "");
    setEditorTemplateJSON(detailData?.templateVO?.templateJson ?? "");

    // 解析时间
    const startTime = dayjs(detailData?.startAt);
    const endTime = dayjs(detailData?.endAt);

    // 格式化为所需的两个字段
    if (startTime.isValid()) {
      const formattedDate = startTime.format("YYYY-MM-DD");
      const formattedTime = startTime.format("HH:mm:ss");
      addFormik.setFieldValue("startTime", formattedDate);
      addFormik.setFieldValue("timeDay", formattedTime);
    }

    if (endTime.isValid()) {
      const formattedDate1 = endTime.format("YYYY-MM-DD");
      const formattedTime1 = endTime.format("HH:mm:ss");
      addFormik.setFieldValue("endTime", formattedDate1);
      addFormik.setFieldValue("endTimeDay", formattedTime1);
    }
  }, [detailData]);

  const [outletMarked, setOutletMarked] = useState(true); // 是否显示门店选择框提示语

  const handleConfirm = (nextIndex, fields) => async (event, isExpanded) => {
    // 检查 fields 是否有效
    if (!fields || !Array.isArray(fields)) {
      console.error("fields is not an array", fields);
      return; // 结束函数执行
    }

    let templateId = fields.find((item) => item == "templateId");

    if (templateId && !addFormik.values["templateId"]) {
      enqueueSnackbar(t("tips.select_template"), {
        variant: "error",
      });

      return;
    }

    // 检查是否所有字段都有值
    const allFieldsFilled = fields?.every((element) => {
      return (
        addFormik.values[element] && !addFormik.errors[element] // 如果某个字段没有值，返回 false
      );
    });

    setOutletMarked(allFieldsFilled);

    if (allFieldsFilled) {
      setExpandedIndex(nextIndex);
    } else {
      try {
        addFormik.setTouched(
          fields.reduce((acc, field) => {
            acc[field] = true; // 标记所有字段为 touched
            return acc;
          }, {})
        );

        addFormik.setTouched({
          ...addFormik.touched,
          ...fields?.reduce((acc, field) => {
            acc[field] = true; // 标记所有字段为 touched
            return acc;
          }, {}),
        });
      } catch (error) {
        return; // 不打开下一个面板
      }
    }
  };

  const handleChange = (index, fields) => (event, isExpanded) => {
    // setExpandedIndex(index);
    const allFieldsFilled = fields?.every((element) => {
      return addFormik.values[element]; // 如果某个字段没有值，返回 false
    });
    if (allFieldsFilled) {
      setExpandedIndex(isExpanded ? index : null);
    } else {
      addFormik.setTouched({
        ...addFormik.touched,
        ...fields?.reduce((acc, field) => {
          acc[field] = true; // 标记所有字段为 touched
          return acc;
        }, {}),
      });
    }
  };

  const handlerCancel = (previousIndex, fields) => (event, isExpanded) => {
    setExpandedIndex(previousIndex);

    // if (previousIndex === "4") {
    //   setExpandedIndex(previousIndex);
    // } else {
    //   // 检查是否所有字段都有值
    //   const allFieldsFilled = fields.every((element) => {
    //     return addFormik.values[element]; // 如果某个字段没有值，返回 false
    //   });

    //   if (allFieldsFilled) {
    //     // 如果所有字段都有值，打开上一个面板
    //     setExpandedIndex(previousIndex);
    //   } else {
    //     addFormik.setTouched(
    //       fields.reduce((acc, field) => {
    //         acc[field] = true; // 标记所有字段为 touched
    //         return acc;
    //       }, {})
    //     );
    //   }
    // }
  };

  return (
    <ChangePriceContext.Provider value={{ outletMarked, setOutletMarked }}>
      <RighViewLayout
        title={
          state?.action === "Edit"
            ? t("events.edit_priceChangeEvent")
            : t("events.add_priceChangeEvent")
        }
        navigateBack={"/price-change-event"}>
        <Grid p={2}>
          <PriceName
            addFormik={addFormik}
            priceNameConfig={priceNameConfig}
            setPriceNameConfig={setPriceNameConfig}
            handleChange={handleChange}
            handleConfirm={handleConfirm}
            setExpandedIndex={setExpandedIndex}
            expandedIndex={expandedIndex}></PriceName>

          <Grid mt={2}>
            <AreaOutlet
              initValue={detailData}
              addFormik={addFormik}
              outletConfig={outletConfig}
              setOutletConfig={setOutletConfig}
              handleChange={handleChange}
              handleConfirm={handleConfirm}
              handlerCancel={handlerCancel}
              setExpandedIndex={setExpandedIndex}
              expandedIndex={expandedIndex}></AreaOutlet>
          </Grid>

          <Grid mt={2}>
            <ProductFilter
              addFormik={addFormik}
              productConfig={productConfig}
              setProductConfig={setProductConfig}
              handleChange={handleChange}
              handleConfirm={handleConfirm}
              handlerCancel={handlerCancel}
              setExpandedIndex={setExpandedIndex}
              expandedIndex={expandedIndex}
              setProductData={setProductData}
              productData={productData}
              detailData={detailData}></ProductFilter>
          </Grid>

          <Grid mt={2}>
            <PriceRule
              addFormik={addFormik}
              priceRuleConfig={priceRuleConfig}
              setPriceRulrConfig={setPriceRulrConfig}
              handleChange={handleChange}
              handleConfirm={handleConfirm}
              handlerCancel={handlerCancel}
              setExpandedIndex={setExpandedIndex}
              expandedIndex={expandedIndex}></PriceRule>
          </Grid>

          <Grid mt={2}>
            <Template
              addFormik={addFormik}
              templateConfig={templateConfig}
              setTemplateConfig={setTemplateConfig}
              handleChange={handleChange}
              handleConfirm={handleConfirm}
              handlerCancel={handlerCancel}
              setExpandedIndex={setExpandedIndex}
              expandedIndex={expandedIndex}
              productData={productData}
              setTemplateJSON={setTemplateJSON}
              editorTemplateJSON={editorTemplateJSON}
              detailData={detailData}></Template>
          </Grid>

          <Grid mt={2}>
            <Schedule
              addFormik={addFormik}
              scheduleConfig={scheduleConfig}
              setScheduleConfig={setScheduleConfig}
              handleChange={handleChange}
              handleConfirm={handleConfirm}
              handlerCancel={handlerCancel}
              setExpandedIndex={setExpandedIndex}
              expandedIndex={expandedIndex}
              productData={productData}
              handlerConfirm={addFormik.handleSubmit}></Schedule>
          </Grid>
        </Grid>
      </RighViewLayout>
    </ChangePriceContext.Provider>
  );
}

export default AddPriceEvent;
