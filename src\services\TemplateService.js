import request from "@/util/request";

const baseUrl = "/nt/v1";

/**
 *   获取分页列表
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getPages = (params) => {
  return request({
    url: `${baseUrl}/template/page`,
    method: "GET",
    params: params,
  });
};

/**
 * 获取模板列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回请求的Promise对象
 */
export const list = (params) => {
  return request({
    url: `${baseUrl}/template`,
    method: "GET",
    params: params,
  });
};

/**
 * 添加新模板
 * @param {Object} params - 新模板的数据
 * @returns {Promise} 返回请求的Promise对象
 */
export const add = (params) => {
  return request({
    url: `${baseUrl}/template`,
    method: "POST",
    data: params,
  });
};

/**
 * 编辑现有模板
 * @param {string|number} id - 要编辑的模板ID
 * @param {Object} params - 更新的模板数据
 * @returns {Promise} 返回请求的Promise对象
 */
export const edit = (id, params) => {
  return request({
    url: `${baseUrl}/template/${id}`,
    method: "PUT",
    data: params,
  });
};

/**
 * 获取模板详情
 * @param {string|number} id - 要获取详情的模板ID
 * @returns {Promise} 返回请求的Promise对象
 */
export const getDetail = (id) => {
  return request({
    url: `${baseUrl}/template/${id}`,
    method: "GET",
  });
};

/**
 * 删除模板
 * @param {Object} params - 要删除的模板数据
 * @returns {Promise} 返回请求的Promise对象
 */
export const deletes = (params) => {
  return request({
    url: `${baseUrl}/template`,
    method: "delete",
    data: params,
  });
};

/**
 * 生成模板图片
 * @param {Object} params - 生成图片所需的参数
 * @returns {Promise} 返回请求的Promise对象
 */
export const getImage = (params) => {
  return request({
    url: `${baseUrl}/template/template_image`,
    method: "POST",
    data: params,
    toast: true,
  });
};

// 获取模板列表设备型号
export const getDeviceModel = () => {
  return request({
    url: `${baseUrl}/template/template_image`,
    method: "POST",
    data: params,
  });
};
