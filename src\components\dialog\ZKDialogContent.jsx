import React from 'react';
import PropTypes from 'prop-types';
import DialogContent from '@mui/material/DialogContent';
const ZKDialogContent = (props) => {
    const { children, ...other } = props;
    return (
        <DialogContent dividers={true} {...other}>
            {children}
        </DialogContent>
    );
};
ZKDialogContent.propTypes = {
    children: PropTypes.node
};

export default ZKDialogContent;
