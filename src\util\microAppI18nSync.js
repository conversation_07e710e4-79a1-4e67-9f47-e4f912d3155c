/**
 * 微前端国际化同步工具
 * 用于在子应用中同步主应用的国际化设置
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

import { setStoreLang, getStoreLang } from '@/util/langUtils';

// 保存主应用传递的props引用
let globalProps = null;

/**
 * 初始化子应用国际化同步
 * @param {Object} i18nInstance - 子应用的i18n实例
 * @param {Object} props - 主应用传递的props
 * @returns {Object} 返回配置信息
 */
export const initSubAppI18n = (i18nInstance, props) => {
  console.log('初始化子应用国际化同步', { props });

  // 保存props引用供后续使用
  globalProps = props;

  let currentLanguage = getStoreLang(); // 默认使用本地存储的语言
  let shouldUseMainAppLanguage = true;

  // 检查是否是用户主动切换语言后的刷新
  // 通过检查本地存储的时间戳来判断
  const lastLanguageChangeTime = localStorage.getItem('lastLanguageChangeTime');
  const now = Date.now();

  // 如果最近5秒内有语言切换操作，优先使用本地存储的语言
  if (lastLanguageChangeTime && (now - parseInt(lastLanguageChangeTime)) < 5000) {
    shouldUseMainAppLanguage = false;
    console.log('检测到最近的语言切换操作，使用本地存储的语言:', currentLanguage);
  } else if (lastLanguageChangeTime) {
    // 清理过期的时间戳
    localStorage.removeItem('lastLanguageChangeTime');
  }

  // 如果主应用传递了语言信息，且应该使用主应用语言
  if (props && props.language && shouldUseMainAppLanguage) {
    // 只有当主应用语言与本地存储不同时才更新
    if (props.language !== currentLanguage) {
      currentLanguage = props.language;
      console.log('使用主应用传递的语言:', currentLanguage);

      // 同步到本地存储
      setStoreLang(currentLanguage);

      // 更新子应用的i18n实例
      if (i18nInstance && typeof i18nInstance.changeLanguage === 'function') {
        i18nInstance.changeLanguage(currentLanguage);
        console.log('子应用语言已更新为:', currentLanguage);
      }
    } else {
      console.log('主应用语言与本地存储一致，无需更新:', currentLanguage);
    }
  } else if (currentLanguage) {
    // 使用本地存储的语言
    console.log('使用本地存储的语言:', currentLanguage);
    if (i18nInstance && typeof i18nInstance.changeLanguage === 'function') {
      i18nInstance.changeLanguage(currentLanguage);
    }
  }

  // 监听主应用的全局状态变化
  if (props && props.actions && typeof props.actions.onGlobalStateChange === 'function') {
    props.actions.onGlobalStateChange((state) => {
      console.log('接收到主应用状态变化:', state);

      if (state.language && state.language !== currentLanguage) {
        currentLanguage = state.language;
        console.log('语言变化，更新为:', currentLanguage);

        // 同步到本地存储
        setStoreLang(currentLanguage);

        // 更新子应用的i18n实例
        if (i18nInstance && typeof i18nInstance.changeLanguage === 'function') {
          i18nInstance.changeLanguage(currentLanguage);
          console.log('子应用语言已同步更新为:', currentLanguage);
        }
      }

      // 处理其他状态变化（如token、用户信息等）
      if (state.token) {
        console.log('接收到token更新');
        // 这里可以处理token同步逻辑
      }

      if (state.user) {
        console.log('接收到用户信息更新');
        // 这里可以处理用户信息同步逻辑
      }
    }, true); // 立即执行一次回调
  }

  return {
    currentLanguage,
    isMainApp: !!props,
    appIdentifier: props?.appIdentifier || 'unknown',
    hasActions: !!(props && props.actions)
  };
};

/**
 * 手动同步语言（用于特殊情况）
 * @param {Object} i18nInstance - i18n实例
 * @param {string} language - 目标语言
 */
export const syncLanguage = (i18nInstance, language) => {
  if (!language) {
    console.warn('syncLanguage: 语言参数为空');
    return;
  }

  console.log('手动同步语言:', language);

  // 记录语言切换时间戳，用于后续判断是否是用户主动切换
  localStorage.setItem('lastLanguageChangeTime', Date.now().toString());

  // 更新本地存储
  setStoreLang(language);

  // 更新i18n实例
  if (i18nInstance && typeof i18nInstance.changeLanguage === 'function') {
    i18nInstance.changeLanguage(language);
    console.log('语言已手动同步为:', language);
  }

  // 如果在微前端环境中，通知主应用语言已变更
  if (globalProps && globalProps.actions && typeof globalProps.actions.setGlobalState === 'function') {
    try {
      globalProps.actions.setGlobalState({ language });
      console.log('已通知主应用语言变更:', language);
    } catch (error) {
      console.error('通知主应用语言变更失败:', error);
    }
  }
};

/**
 * 获取当前语言
 * @returns {string} 当前语言代码
 */
export const getCurrentLanguage = () => {
  return getStoreLang();
};

/**
 * 检查是否在微前端环境中
 * @param {Object} props - 主应用传递的props
 * @returns {boolean} 是否在微前端环境
 */
export const isMicroAppEnvironment = (props) => {
  return !!(props && (props.actions || props.appIdentifier));
};

/**
 * 子应用国际化配置辅助函数
 * @param {Object} i18nInstance - i18n实例
 * @param {Object} props - 主应用传递的props
 * @param {Object} options - 额外配置选项
 * @returns {Object} 配置结果
 */
export const configureSubAppI18n = (i18nInstance, props, options = {}) => {
  const {
    enableDebug = false,
    fallbackLanguage = 'en',
    onLanguageChange = null
  } = options;

  if (enableDebug) {
    console.log('配置子应用国际化:', { props, options });
  }

  // 初始化同步
  const config = initSubAppI18n(i18nInstance, props);

  // 如果提供了语言变化回调，则注册监听
  if (onLanguageChange && typeof onLanguageChange === 'function') {
    if (props && props.actions) {
      props.actions.onGlobalStateChange((state) => {
        if (state.language) {
          onLanguageChange(state.language, config.currentLanguage);
        }
      });
    }
  }

  // 设置fallback语言
  if (i18nInstance && typeof i18nInstance.changeLanguage === 'function') {
    if (!config.currentLanguage) {
      i18nInstance.changeLanguage(fallbackLanguage);
      setStoreLang(fallbackLanguage);
      config.currentLanguage = fallbackLanguage;
    }
  }

  return config;
};

/**
 * 清理国际化同步（在子应用卸载时调用）
 * @param {Object} props - 主应用传递的props
 */
export const cleanupI18nSync = (props) => {
  console.log('清理国际化同步');

  // 清理全局props引用
  globalProps = null;

  // 这里可以添加清理逻辑，比如移除事件监听器等
  // qiankun会自动处理大部分清理工作，但如果有特殊需求可以在这里处理

  if (props && props.actions) {
    // 如果需要手动清理全局状态监听，可以在这里处理
    console.log('清理全局状态监听');
  }
};

export default {
  initSubAppI18n,
  syncLanguage,
  getCurrentLanguage,
  isMicroAppEnvironment,
  configureSubAppI18n,
  cleanupI18nSync
};