import React, { useEffect, useState } from "react";

import "./css/App.css";
import Routes from "@/router/routers.js";
import onRouteBefore from "@/router/onRouteBefore.js";
import Loader from "@/router/Component/Loader.jsx";
import RouterWaiter from "@/router/routerWaiter/index.jsx";
import { SnackbarProvider } from "notistack";
import { ToastContainer, Zoom } from "react-toastify";
import { getUserMenus } from "@/services/common.js";
import { useDispatch } from "react-redux";
import { useDispatchMenu } from "@/store/hooks/menu";
function App() {
  const dispatch = useDispatch();

  const { stateSetMenu } = useDispatchMenu();

  useEffect(() => {
    const fetchData = async () => {
      // 无论上面的 try-catch 块是否成功执行，都会执行这里的代码
      try {
        const menuRes = await getUserMenus({ applicationCode: "NT" });
        let menus = menuRes.data;
        dispatch(stateSetMenu(menus));
      } catch (error) {
        console.error("Error fetching user menus:", error);
      }
    };

    fetchData();
  }, []);

  return (
    <SnackbarProvider
      maxSnack={3}
      autoHideDuration={3000}
      anchorOrigin={{
        vertical: "top",
        horizontal: "center",
      }}
      style={{
        marginTop: "300px",
      }}>
      <ToastContainer
        position="top-center"
        style={{
          fontSize: "16px",
        }}
        autoClose={5000}
        hideProgressBar
        newestOnTop={false}
        closeOnClick
        rtl={false}
        limit={5}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        transition={Zoom}
      />
      <RouterWaiter
        routes={Routes}
        loading={<Loader />}
        onRouteBefore={onRouteBefore}
      />
    </SnackbarProvider>
  );
}

export default App;
