import React from "react";
import { backdropClasses, Tooltip } from "@mui/material";
import SvgIcon from "@c/SvgIcon.jsx";
import { useTranslation } from "react-i18next";
function Callback() {
  const { t } = useTranslation();
  const handleToggle = () => {
    window.location.href = "/application/center";
  };

  return (
    <React.Fragment>
      <Box sx={{ flexShrink: 0, pr: 2 }}>
        <Tooltip title={t("common.back")} arrow placement="bottom">
          <IconButton
            disableRipple
            color="secondary"
            sx={{
              color: "text.primary",
              backgroundColor: "grey.100",
            }}
            aria-label="open profile"
            aria-haspopup="true"
            onClick={handleToggle}>
            <SvgIcon localIcon={"CallBack"}></SvgIcon>
          </IconButton>
        </Tooltip>
      </Box>
    </React.Fragment>
  );
}

export default Callback;
