import React from "react";
import MainLayout from "@/layout/MainLayout/index.jsx";
import dashboardRoute from "./dashboard.jsx";
import priceRoute from "./price.jsx";
import templateRoute from "./templates.jsx";
import editorRoute from "./editor.jsx";
import NewEditor from "@/pages/editor/index.jsx";
import pictureRoute from "./picture.jsx";
import gatewayRoute from "./gateway.jsx";
import screensRoute from "./screen.jsx";
import systemLogRoute from "./systemlog.jsx";
const bizRoutes = [
  {
    path: "/",
    element: <MainLayout />,
    children: [
      ...dashboardRoute,
      ...priceRoute,
      ...templateRoute,
      ...pictureRoute,
      ...gatewayRoute,
      ...screensRoute,
      ...systemLogRoute,
    ],
  },

  {
    path: "/editor",
    element: <NewEditor />,
    children: [...editorRoute],
  },
];
export default bizRoutes;
