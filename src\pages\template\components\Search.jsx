import React from "react";
import SearchIcon from "@mui/icons-material/Search";
import { useTranslation } from "react-i18next";
import { list } from "@s/resolution.js";
function Search(props) {
  const {
    setFilters,
    resolution = null,
    setResolution,
    templateValue = null,
    setTemplateValue,
    templateTypeOptions = [],
  } = props;
  const { t } = useTranslation();
  const [resolutionOptions, setResolutionOptions] = useState([]);

  const handleResolutionChange = () => {
    setFilters({
      ...defaultFilters,
      screenResolution: resolution,
      type: templateValue?.id,
      operator: "AND",
    });
  };

  const handleReset = () => {
    setResolution(null); // 改为 null 而不是空字符串
    setTemplateValue(null); // 改为 null 而不是空字符串
    setFilters({ ...defaultFilters });
  };

  useEffect(() => {
    list().then((res) => {
      setResolutionOptions(res?.data?.resolutionList || []);
    });
  }, []);

  return (
    <React.Fragment>
      <Grid container>
        <Typography variant="title" sx={{ fontSize: "20px" }}>
          {t("menu.template_list")}
        </Typography>
        <Grid
          container
          xs={12}
          spacing={1}
          style={{
            height: "100px",
            background: "#FFFFFF",
            borderRadius: "8px",
            marginTop: "12px",
          }}>
          <Grid item xs={2}>
            <Typography
              sx={{ fontSize: "12px", color: "#474B4F", opacity: "0.8" }}>
              {t("template.resolution")}
            </Typography>
            <Autocomplete
              noOptionsText={t("tips.no_options")}
              options={resolutionOptions || []}
              value={resolution}
              onChange={(e, v) => {
                setResolution(v);
              }}
              isOptionEqualToValue={(option, value) =>
                (!option && !value) ||
                (option && value && option.value === value.value)
              }
              getOptionLabel={(option) => (option ? option.resolution : "")}
              renderInput={(params) => <TextField {...params} size="small" />}
            />
          </Grid>
          <Grid item xs={2}>
            <Typography
              sx={{ fontSize: "12px", color: "#474B4F", opacity: "0.8" }}>
              {t("template.type")}
            </Typography>
            <Autocomplete
              noOptionsText={t("tips.no_options")}
              options={templateTypeOptions || []}
              value={templateValue}
              onChange={(e, v) => {
                setTemplateValue(v);
              }}
              isOptionEqualToValue={(option, value) =>
                (!option && !value) ||
                (option && value && option.value === value.value)
              }
              getOptionLabel={(option) => (option ? option.value : "")}
              renderInput={(params) => <TextField {...params} size="small" />}
            />
          </Grid>

          <Grid item mt={2.5} ml={4}>
            <Button
              id="index1"
              variant="outlined"
              size="medium"
              className="text-transform-none"
              onClick={handleResolutionChange}
              style={{
                background: "linear-gradient(45deg, #1487CA, #78BC27)",
                marginRight: "10px",
                color: "#FFFF",
              }}>
              {t("common.find")}
            </Button>
            <Button
              id="index2"
              className="text-transform-none"
              variant="outlined"
              size="medium"
              style={{ marginRight: "10px" }}
              onClick={handleReset}>
              {t("common.reset")}
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default Search;
