import dayjs from "dayjs";
import CommonUtil from "@u/CommonUtils";
import i18next from "i18next";
export const getColums = (preView) => {
  let columns = [
    {
      field: "screenId",
      headerName: i18next.t("synchronized.screenID"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Tooltip id="Logs-1" title={e.row.screenId} arrow placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.screenId)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "outletName",
      headerName: i18next.t("synchronized.outlet"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Tooltip
            id="Logs-2"
            title={e.row.outletName}
            arrow
            placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.outletName)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "productName",
      headerName: i18next.t("synchronized.product"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Tooltip
            id="Logs-3"
            title={e.row.productName}
            arrow
            placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.productName)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "deviceSn",
      headerName: i18next.t("synchronized.deviceSN"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Tooltip id="Logs-4" title={e.row.deviceSn} arrow placement="bottom">
            <span>{CommonUtil.formatLongText(e.row.deviceSn)}</span>
          </Tooltip>
        </>
      ),
    },
    {
      field: "status",
      headerName: i18next.t("synchronized.status"),
      flex: 1,
      renderCell: (e) => {
        const { color, text } = renderStatus(e.row.status);
        return (
          <Tooltip id="Logs-5" title={text} arrow placement="bottom">
            <span style={{ color }}>{text}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "url",
      headerName: i18next.t("synchronized.picture"),
      flex: 1,
      renderCell: (e) => {
        return (
          <Avatar
            variant="square"
            onClick={() => {
              preView(e.row);
            }}
            alt="Remy Sharp"
            src={e.row.url}
            sx={{ width: 34, height: 34 }}
          />
        );
      },
    },
    {
      field: "createdAt",
      headerName: i18next.t("table_approval.create_time"),
      flex: 1,
      renderCell: (e) => (
        <>
          <Tooltip
            id="Logs-6"
            title={dayjs(e.row.createdAt).format("YYYY-MM-DD HH:mm:ss")}
            arrow
            placement="bottom">
            <span>
              {CommonUtil.formatLongText(
                dayjs(e.row.createdAt).format("YYYY-MM-DD HH:mm:ss")
              )}
            </span>
          </Tooltip>
        </>
      ),
    },
  ];

  return columns;
};

const renderStatus = (status) => {
  let color = "black";
  let text = i18next.t("common.created");
  if (status === 1) {
    color = "green";
    text = i18next.t("common.success");
  } else if (status === 0) {
    color = "red";
    text = i18next.t("common.fail");
  } else if (status === -1) {
    color = "orange";
    text = i18next.t("common.device_deleted");
  }
  return { color, text };
};
