import request from "@/util/request";

/**
 *   获取变价事件列表
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getPriceChangeEvents = (params) => {
  return request({
    url: `/nt/v1/price_change_event/page`,
    method: "GET",
    params: params,
  });
};

/**
 *   获取删除变价事件
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const deletePriceChangeEvent = (params) => {
  return request({
    url: `/nt/v1/price_change_event`,
    method: "DELETE",
    data: params,
  });
};

/**
 *   新增变价事件
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const addChangeEvent = (params) => {
  return request({
    url: `/nt/v1/price_change_event`,
    method: "POST",
    data: params,
  });
};

/**
 *   修改删除变价事件
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const editChangeEvent = (id, params) => {
  return request({
    url: `/nt/v1/price_change_event/${id}`,
    method: "PUT",
    data: params,
  });
};

/**
 *   获取变价事件详情
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getDetail = (id) => {
  return request({
    url: `/nt/v1/price_change_event/${id}`,
    method: "GET",
  });
};

/**
 *
 * 根据产品和选中的促销类型查询所有分辨率的模板;
 *
 * @argument属性值  productId  promotionType
 * <AUTHOR>
 * @date 2025-02-18 14:35
 */

export const getTemplates = (params) => {
  return request({
    url: `/nt/v1/template/event_template`,
    method: "GET",
    params: params,
  });
};

export const OnlineOutlets = (params) => {
  return request({
    url: `${import.meta.env.VITE_GLOBAL}/outlet/device_online_outlet_list`,
    method: "GET",
    params: params,
  });
};
