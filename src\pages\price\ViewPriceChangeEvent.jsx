import { Box, Paper } from "@mui/material";
import RightViewLayout from "../../components/RighViewLayout";
import OverFlowText from "@c/OverFlowText";
import { useEffect, useState } from "react";
import { getDetail } from "@s/price";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

export default function ViewPriceChangeEvent() {
  const { t } = useTranslation();
  const { state } = useLocation();
  const [filters, setFilters] = useState({
    pageNumber: 1,
    pageSize: 5,
  });
  const [product, setProduct] = useState({});
  const [productName, setProductName] = useState();
  const [priceChangeRuleId, setPriceChangeRuleId] = useState();
  const [templateId, setTemplateId] = useState();
  const [scheduleTime, setScheduleTime] = useState();
  const [startAt, setStartAt] = useState();
  const [endAt, setEndAt] = useState();
  const [days, setDays] = useState([]);
  const [dates, setDates] = useState([]);

  const dateFormat = (date) => {
    const newDate = new Date(date);
    const day = String(newDate.getDate()).padStart(2, "0");
    const month = String(newDate.getMonth() + 1).padStart(2, "0");
    const year = newDate.getFullYear();
    const hours = String(newDate.getHours()).padStart(2, "0");
    const minutes = String(newDate.getMinutes()).padStart(2, "0");
    const seconds = String(newDate.getSeconds()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  };
  const promotionType = [
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];
  useEffect(() => {
    getDetail(state.id).then((res) => {
      if (res?.code === "00000000") {
        setProduct(res.data.data);

        setProductName(res.data.productName);
        setTemplateId(res.data.templateName);

        setScheduleTime(dateFormat(res.data.startAt));
        setStartAt(dateFormat(res.data.startAt));
        setEndAt(res.data?.endAt ? dateFormat(res.data.data.endAt) : null);
        setDays(res.data?.days?.map((day) => day + ", "));
        setDates(res.data?.dates?.map((date) => date + ", "));

        let resdata = promotionType.find(
          (item) => item.id == res.data.promotionType
        );

        setPriceChangeRuleId(resdata?.value);
      }
    });
  }, []);

  return (
    <RightViewLayout
      title={t("events.view_priceChangeEvent")}
      navigateBack={"-1"}>
      <Paper
        elevation={0}
        style={{
          backgroundColor: "#ffffff",
          borderRadius: "8px",
          paddingTop: "20px",
          paddingBottom: "36px",
          paddingLeft: "26px",
          border: "1px solid #DDDDDD",
          margin: "0 1rem 2rem 0",
          overflow: "hidden",
        }}>
        <Box>
          <Box pt={2}>
            <Box>
              <OverFlowText variant="fieldLabel">
                {t("table_product.product_name")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue">
              {productName ? productName : "-"}
            </OverFlowText>
            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.priceChangeRule")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue">
              {priceChangeRuleId ? priceChangeRuleId : "-"}
            </OverFlowText>
            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.templateName")} :
              </OverFlowText>
            </Box>

            <OverFlowText variant="fieldValue">
              {templateId ? templateId : "-"}
            </OverFlowText>
            {/* <Box mt={2}>
                            <OverFlowText variant='fieldLabel'>Schedule Time :</OverFlowText>
                        </Box> */}
            {/* <OverFlowText variant='fieldValue'>{scheduleTime  ? scheduleTime : '-'}</OverFlowText> */}
            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.startDate")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue">
              {startAt ? startAt : "-"}
            </OverFlowText>

            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.endDate")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue">
              {endAt ? endAt : "-"}
            </OverFlowText>

            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.days")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue">
              {days ? days : "-"}
            </OverFlowText>

            <Box mt={2}>
              <OverFlowText variant="fieldLabel">
                {t("events.dates")} :
              </OverFlowText>
            </Box>
            <OverFlowText variant="fieldValue" maxLength={50}>
              {dates ? dates : "-"}
            </OverFlowText>
          </Box>
        </Box>
      </Paper>
    </RightViewLayout>
  );
}
