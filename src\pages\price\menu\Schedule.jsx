import React, { useEffect, useState } from "react";
import { getScheduleConfig } from "../js/config";
import { useTranslation } from "react-i18next";
import Accourdion from "@c/Accordion";
import CmpFormik from "@c/Config/CmpFormik";
import { Grid, Radio, FormControlLabel } from "@mui/material";
import SetRepeatCondition from "./SetRepeatCondition";
import { timeZoneList } from "@/constants/TimeZone";
import { useNavigate } from "react-router-dom";

function Schedule(props) {
  const {
    addFormik,
    scheduleConfig,
    setScheduleConfig,
    expandedIndex,
    handleChange,
    handlerCancel,
    handlerConfirm,
  } = props;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [does, setDoes] = useState(false);

  useEffect(() => {
    let configInfo = getScheduleConfig(t, does, timeZoneList);
    setScheduleConfig(configInfo);
  }, [does, t]);

  useEffect(() => {
    setDoes(addFormik.values.endOrNot == "1" ? true : false);
  }, [addFormik.values.endOrNot]);

  return (
    <Accourdion
      elevation={0}
      expanded={expandedIndex === 5}
      label={t("events.schedule")}
      onChange={handleChange(5, ["scheduleMode", "startTime", "timeDay"])}
      expandedIndex={expandedIndex}>
      <CmpFormik sx={4} formik={addFormik} formConfig={scheduleConfig}>
        <Grid mt={4} xs={12} position={"relative"}>
          {addFormik.values["scheduleMode"] &&
            addFormik.values["scheduleMode"] !== "Does not repeat" && (
              <SetRepeatCondition addFormik={addFormik}></SetRepeatCondition>
            )}
          <Grid
            sx={{
              position: "absolute",
              top: "-80px",
              right: "200px",
            }}>
            <FormControlLabel
              control={
                <Radio
                  id={`role-radio-1`}
                  checked={does}
                  value={does}
                  onClick={() => {
                    const newValue = !does; // 切换值
                    setDoes(newValue); // 更新状态
                    addFormik.setFieldValue("endOrNot", newValue); // 更新 Formik 的值
                  }}
                  name="endOrNot"
                />
              }
              label={t("events.doesNotEnd")}
            />
          </Grid>
        </Grid>
      </CmpFormik>

      <Grid container xs={12} mt={4}>
        <Box
          display={"flex"}
          flexDirection={"row-reverse"}
          style={{
            marginLeft: "1200px",
          }}>
          <Box item pl={2}>
            <Button
              id="AddAuthorizationLevel-button-01"
              variant="contained"
              size="large"
              className="text-transform-none"
              style={{
                size: "medium",
                borderRadius: "8px",
                opacity: 1,
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}
              onClick={() => handlerConfirm()}>
              {t("common.save")}
            </Button>
          </Box>
          <Box item>
            <Button
              id="AddAuthorizationLevel-button-01"
              variant="none"
              size="large"
              className="text-transform-none"
              style={{
                size: "medium",
                borderRadius: "8px",
                opacity: 1,
                color: "#1487CA",
              }}
              onClick={() => navigate("/price-change-event")}>
              {t("common.cancel")}
            </Button>
          </Box>
        </Box>
      </Grid>
    </Accourdion>
  );
}

export default Schedule;
