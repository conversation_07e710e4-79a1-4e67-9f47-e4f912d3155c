import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Box } from "@mui/material";
function TotalRefreshTime(props) {
  const { dashBoardData } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Box
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}>
        <Typography variant="h6" fontWeight={"bold"}>
          {t("dashboard.total_refreshes")}
        </Typography>
        <Typography
          style={{
            color: "gray",
            fontSize: "14px",
            width: "70%",
            textAlign: "center",
          }}>
          {t("dashboard.total_number")}
        </Typography>
        <Box alignSelf={"center"}>
          <Typography
            style={{
              fontWeight: "bold",
              color: "#3e9c88",
            }}
            variant="h1">
            {dashBoardData?.refreshCountOfTotal}
          </Typography>
        </Box>
        <Typography variant="h5" fontWeight={"bold"}>
          {t("dashboard.refreshes")}
        </Typography>
      </Box>
    </React.Fragment>
  );
}

export default TotalRefreshTime;
