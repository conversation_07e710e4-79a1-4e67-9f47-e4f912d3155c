import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Grid,
  InputLabel,
  FormControlLabel,
  Radio,
  Typography,
} from "@mui/material";
import CustomInput from "../../../components/CustomInput";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import SetRepeatCondition from "./SetRepeatCondition";

import CustomDatePicker from "../../../components/CustomDatePicker";
import TimePickerDate from "../../../components/TimePickerDate";
import dayjs from "dayjs";
import { useStyles, splitDateTime } from "../js/way";

function ChangeSchedule(props) {
  const { t } = useTranslation();
  const classes = useStyles();

  const {
    isScheduleExpanded,
    setPayload,
    payload,
    setTimezone,
    timezone,
    timeZoneList,
    fullDay,
    setFullDay,
    startDayDate,
    startTimeSate,
    doesNotEnd,
    setDoesNotEnd,
    endDayDate,
    endTimeDate,
    scheduleMode,
    morningTime,
    afterTime,
    selectedYears,
    setSelectedYears,
    selectedMonth,
    setSelectedMonth,
    selectedDays,
    setSelectedDays,
    selectedDates,
    setSelectedDates,
    setStartDayDate,
    setStartTimeDate,
    setEndDayDate,
    setEndTimeDate,
    setScheduleMode,
    setAfterTime,
    setMorningTime,
    scheduleModes,
    handleToggleScheduleAccordion,
    handleScheduleSubmit,
    handleToggleSchedulePrevious,
    promotionValue,
    startPromotion,
    endPromotion,
    IsAllowEdit,
  } = props;
  // console.log("DDDDDDDDDDDDDDDDD", scheduleMode);
  const handleScheduleCancel = () => {
    setStartDayDate(null);
    setStartTimeDate(null);
    setEndDayDate(null);
    setEndTimeDate(null);
    setScheduleMode(scheduleModes[0]);
    setMorningTime(null);
    setAfterTime(null);
    setSelectedYears([]);
    setSelectedMonth([]);
    setSelectedDays([]);
  };

  useEffect(() => {
    setStartDayDate(null);
    setStartTimeDate(null);
    setEndDayDate(null);
    setEndTimeDate(null);
    setMorningTime(null);
    setAfterTime(null);
    setSelectedYears([]);
    setSelectedMonth([]);
    setSelectedDays([]);
    setFullDay(false);
  }, [scheduleMode]);

  useEffect(() => {
    if (doesNotEnd) {
      setEndDayDate(null);
      setEndTimeDate(null);
    }
  }, [doesNotEnd]);

  return (
    <>
      <Accordion
        elevation={0}
        expanded={isScheduleExpanded}
        onChange={handleToggleScheduleAccordion}>
        <AccordionSummary
          expandIcon={<GridExpandMoreIcon />}
          aria-controls="panel3-content"
          id="panel3-header"
          sx={{ fontFamily: "Roboto" }}>
          {t("events.schedule")}
        </AccordionSummary>
        <AccordionDetails>
          <Grid container columnSpacing={1}>
            <Grid item lg={3}>
              <InputLabel shrink htmlFor="bootstrap-input">
                {t("events.scheduleMode")}{" "}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>
              <Autocomplete
                noOptionsText={t("LVLGF0011")}
                options={scheduleModes}
                value={scheduleMode}
                defaultValue={scheduleModes[0]}
                onChange={(e, v) => {
                  if (v && v.value) {
                    setScheduleMode(v);
                    setPayload({
                      ...payload,
                      scheduleMode: v.value,
                    });
                  }
                }}
                getOptionLabel={(option) => (option.value ? option.value : "")}
                renderInput={(params) => (
                  <CustomInput
                    fullWidth
                    value={scheduleModes[0]}
                    handleChange={(e) => {
                      setSelectedYears([]);
                      setSelectedMonth([]);
                      setSelectedDays([]);
                    }}
                    resetError={() => console.log("6666")}
                    size={props.size ? props.size : " "}
                    style={{ width: "100%" }}
                    {...params}
                    inputProps={{
                      ...params.inputProps,
                      placeholder: props.placeholder,
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item lg={3}>
              <InputLabel shrink htmlFor="bootstrap-input">
                {t("LVLOT0009")}
              </InputLabel>
              <Autocomplete
                disabled
                id="AddDeviceTimeZoneList"
                options={timeZoneList}
                value={timezone}
                onChange={(e, v) => {
                  setTimezone(v);
                }}
                getOptionLabel={(option) => (option.name ? option.name : "")}
                renderInput={(params) => (
                  <CustomInput
                    fullWidth
                    value={timezone}
                    handleChange={(e) => console.log()}
                    resetError={() => console.log("6666")}
                    size={props.size ? props.size : " "}
                    style={{ width: "100%" }}
                    {...params}
                    inputProps={{
                      ...params.inputProps,
                      placeholder: props.placeholder,
                    }}
                  />
                )}
              />
            </Grid>
          </Grid>

          <Grid columnSpacing={1} pt={2} display={"flex"}>
            <Box>
              <InputLabel shrink htmlFor="bootstrap-input">
                {t("events.startPromotion")}{" "}
                <span style={{ color: "red" }}>*</span>
              </InputLabel>

              <Grid display={"flex"}>
                <Box width={185}>
                  <CustomDatePicker
                    SelectedDate={(e) =>
                      setStartDayDate(dayjs(e).format("YYYY-MM-DD"))
                    }
                    date={startDayDate}
                    placeholder={"Start Date"}
                    minDate={new Date()}
                  />
                </Box>

                <Box width={185} ml={3}>
                  <TimePickerDate
                    onChange={(e) => setStartTimeDate(e)}
                    value={startTimeSate}
                    placeholder={
                      startTimeSate !== null ? startTimeSate : "Start Time"
                    }></TimePickerDate>
                </Box>
              </Grid>
            </Box>
            {doesNotEnd === false ? (
              <Box ml={1}>
                <InputLabel shrink htmlFor="bootstrap-input">
                  {t("events.endPromotion")}
                </InputLabel>
                <Grid display={"flex"}>
                  <Box width={185}>
                    <CustomDatePicker
                      SelectedDate={(e) =>
                        setEndDayDate(dayjs(e).format("YYYY-MM-DD"))
                      }
                      date={endDayDate}
                      placeholder={"End Date"}
                      minDate={new Date()}></CustomDatePicker>
                  </Box>

                  <Box width={185} ml={3}>
                    <TimePickerDate
                      onChange={(e) => setEndTimeDate(e)}
                      value={endTimeDate}
                      placeholder={
                        endTimeDate !== null ? endTimeDate : "End Time"
                      }></TimePickerDate>
                  </Box>
                </Grid>
              </Box>
            ) : null}

            <Box ml={6} mt={4}>
              <FormControlLabel
                control={
                  <Radio
                    id={`role-radio-1}`}
                    checked={doesNotEnd}
                    onClick={() => {
                      setDoesNotEnd((prevDoesNotEnd) => !prevDoesNotEnd);
                    }}
                    name="selection"
                  />
                }
                label={"Does not end"}
              />
            </Box>
          </Grid>

          {scheduleMode && scheduleMode.id === 1 ? null : (
            <Grid>
              <Typography mt={2} fontWeight={700}>
                {t("events.setRepeatCondition")}
              </Typography>
              <Grid container columnSpacing={1} bgcolor={"#fafafb"}>
                <SetRepeatCondition
                  scheduleMode={scheduleMode}
                  fullDay={fullDay}
                  setFullDay={setFullDay}
                  morningTime={morningTime}
                  setMorningTime={setMorningTime}
                  afterTime={afterTime}
                  setAfterTime={setAfterTime}
                  selectedYears={selectedYears}
                  setSelectedYears={setSelectedYears}
                  selectedMonth={selectedMonth}
                  setSelectedMonth={setSelectedMonth}
                  selectedDays={selectedDays}
                  setSelectedDays={setSelectedDays}
                  startDayDate={startDayDate}
                  endDayDate={endDayDate}
                  selectedDates={selectedDates}
                  setSelectedDates={setSelectedDates}
                />
              </Grid>
            </Grid>
          )}

          <Grid container spacing={2} pb={2}>
            <Grid item xs={12}>
              <Box display={"flex"} flexDirection={"row-reverse"}>
                <Box item pl={2}>
                  <Button
                    disabled={!IsAllowEdit}
                    id="AddAuthorizationLevel-button-01"
                    variant="contained"
                    size="large"
                    className="text-transform-none"
                    style={{
                      size: "medium",
                      borderRadius: "8px",
                      opacity: 1,
                      background:
                        "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                    }}
                    onClick={handleScheduleSubmit}>
                    {t("common.save")}
                  </Button>
                </Box>
                <Box item pl={2}>
                  <Button
                    id="AddAuthorizationLevel-button-02"
                    className="text-transform-none"
                    variant="outlined"
                    onClick={handleToggleSchedulePrevious}
                    size="large">
                    {t("common.previous")}
                  </Button>
                </Box>
                <Box item>
                  <Button
                    id="AddAuthorizationLevel-button-01"
                    variant="none"
                    size="large"
                    className="text-transform-none"
                    style={{
                      size: "medium",
                      borderRadius: "8px",
                      opacity: 1,
                      color: "#1487CA",
                    }}
                    onClick={handleScheduleCancel}>
                    {t("common.previous")}
                  </Button>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
    </>
  );
}
export default ChangeSchedule;
