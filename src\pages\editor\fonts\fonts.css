@font-face {
  font-family: "Microsoft YaHei UI Light";
  src: url("./02e1d01d9d9dcde4b8c88b6d9f04b9d0.ttf");
}

@font-face {
  font-family: "Bebas";
  src: url("./8438aadc1cd4a03262f91a83642dff3b.ttf");
}

@font-face {
  font-family: "Impact";
  src: url("./4681232598ef0a5b6515cd80190813f7.ttf");
}

@font-face {
  font-family: "Heavy";
  src: url("./ab6101ac2d330aec30c788521ef0a2c4.ttf");
}

@font-face {
  font-family: "方正粗黑宋简体";
  src: url("./b96f239880a100e34c0f07dd7a378aa7.ttf");
}

@font-face {
  font-family: "Corbel";
  src: url("./cf8fe33a677fb1644e301c632eccc45c.ttf");
}

@font-face {
  font-family: "mnjhzbg";
  src: url("./mnjhzgb.ttf");
}

@font-face {
  font-family: "文泉驿微米黑";
  src: url("./wenquanyi.ttf");
}

@font-face {
  font-family: "zfull-GB";
  src: url("./zfull-GB.ttf");
}

@font-face {
  font-family: "Fangzheng-Cu";
  src: url("./Fangzheng-Cu.ttf");
}

@font-face {
  font-family: "Microsoft-YaHei-UI-Light";
  src: url("./Microsoft-YaHei-UI-Light.ttf");
}

@font-face {
  font-family: "WenQuanYi-Micro-Hei";
  src: url("./WenQuanYi-Micro-Hei.ttf");
}

@font-face {
  font-family: "Noto-Sans-Thai-Looped-Thin";
  src: url("./Noto-Sans-Thai-Looped-Thin.ttf");
}

@font-face {
  font-family: "Noto-Sans-Thai-Looped-Light";
  src: url("./Noto-Sans-Thai-Looped-Light.ttf");
}

@font-face {
  font-family: "Noto-Sans-Thai-Looped-Regular";
  src: url("./Noto-Sans-Thai-Looped-Regular.ttf");
}
