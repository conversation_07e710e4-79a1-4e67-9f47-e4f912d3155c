{"javascript": {"globals": ["Accordion", "AccordionActions", "AccordionDetails", "AccordionSummary", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AppBar", "Autocomplete", "Avatar", "AvatarGroup", "Backdrop", "Badge", "BottomNavigation", "BottomNavigationAction", "Box", "Breadcrumbs", "<PERSON><PERSON>", "ButtonBase", "ButtonGroup", "Card", "CardActionArea", "CardActions", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Checkbox", "Chip", "CircularProgress", "ClickAwayListener", "Collapse", "Container", "CssBaseline", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "Divider", "Drawer", "Fab", "Fade", "FilledInput", "FormControl", "FormControlLabel", "FormGroup", "FormHelperText", "FormLabel", "GlobalStyles", "Grid", "Grow", "Hidden", "Icon", "IconButton", "ImageList", "ImageListItem", "ImageListItemBar", "Input", "InputAdornment", "InputBase", "InputLabel", "LinearProgress", "Link", "List", "ListItem", "ListItemAvatar", "ListItemButton", "ListItemIcon", "ListItemSecondaryAction", "ListItemText", "ListSubheader", "<PERSON><PERSON>", "MenuItem", "MenuList", "MobileStepper", "Modal", "NativeSelect", "NavLink", "Navigate", "NoSsr", "Outlet", "OutlinedInput", "Pagination", "PaginationItem", "Paper", "Popover", "<PERSON><PERSON>", "Portal", "Radio", "RadioGroup", "Rating", "Route", "Routes", "ScopedCssBaseline", "Select", "Skeleton", "Slide", "Slide<PERSON>", "Snackbar", "SnackbarContent", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "<PERSON><PERSON>", "Step", "StepButton", "StepConnector", "<PERSON><PERSON><PERSON><PERSON>", "StepIcon", "<PERSON><PERSON><PERSON><PERSON>", "Stepper", "SvgIcon", "SwipeableDrawer", "Switch", "Tab", "TabScrollButton", "Table", "TableBody", "TableCell", "TableContainer", "TableFooter", "TableHead", "TablePagination", "TableRow", "TableSortLabel", "Tabs", "TextField", "TextareaAutosize", "ToggleButton", "ToggleButtonGroup", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Typography", "Unstable_Grid2", "Zoom", "createRef", "darkScrollbar", "dayjs", "forwardRef", "generateUtilityClass", "generateUtilityClasses", "lazy", "memo", "startTransition", "useAutocomplete", "useCallback", "useContext", "useDebugValue", "useDeferredValue", "useEffect", "useHref", "useId", "useImperativeHandle", "useInRouterContext", "useInsertionEffect", "useLayoutEffect", "useLinkClickHandler", "useLocation", "useMediaQuery", "useMemo", "useNavigate", "useNavigationType", "useOutlet", "useOutletContext", "useParams", "useReducer", "useRef", "useResolvedPath", "useRoutes", "useScrollTrigger", "useSearchParams", "useState", "useSyncExternalStore", "useTransition", "useTranslation"]}}