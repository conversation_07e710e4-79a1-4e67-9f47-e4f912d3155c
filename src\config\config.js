// ==============================|| 主题配置  ||============================== //

const config = {
  //登录后首页地址
  defaultPath: "/dashboard",
  fontFamily: `'Public Sans', sans-serif`,
  //默认语言
  i18n: "en",
  miniDrawer: false,
  container: true,
  mode: "light",
  presetColor: "default",
  themeDirection: "ltr",
};

export default config;
//菜单的宽度
export const drawerWidth = 300;

export const twitterColor = "#1DA1F2";
export const facebookColor = "#3b5998";
export const linkedInColor = "#0e76a8";
