import React, { useEffect, useState } from "react";
import { getProductConfig } from "../js/config";
import { useTranslation } from "react-i18next";
import Accourdion from "@c/Accordion";
import CmpFormik from "@c/Config/CmpFormik";
import {
  getProductBrandInfo,
  getProductLabelData,
  getProductInfo,
} from "../js/interface";
function ProductFilter(props) {
  const {
    addFormik,
    productConfig,
    setProductConfig,
    expandedIndex,
    handleChange,
    handleConfirm,
    handlerCancel,
    detailData,
    productData,
    setProductData,
  } = props;
  const { t } = useTranslation();
  const [brandData, setBrandData] = useState([]);
  const [selectLabel, setSelectLabel] = useState("");
  const [productLabel, setProductLabel] = useState([]);

  // 获取Label 列表数据
  useEffect(() => {
    getProductLabelData(setProductLabel);
  }, []);

  // 获取商品标签
  useEffect(() => {
    let res = productLabel?.find(
      (item) => item.id == detailData?.productAttributeId
    );

    setSelectLabel(res?.label);
  }, [addFormik.values?.productAttributeId]);

  // 获取品牌列表
  useEffect(() => {
    if (addFormik.values.productAttributeId) {
      getProductBrandInfo(addFormik.values.productAttributeId, setBrandData);
    }
  }, [addFormik.values.productAttributeId]);

  //获取商品列表
  useEffect(() => {
    if (
      addFormik.values?.productAttributeValue &&
      addFormik.values.productAttributeId
    ) {
      getProductInfo(
        addFormik.values.productAttributeId,
        addFormik.values?.productAttributeValue,
        setProductData
      );
    }
  }, [addFormik.values?.productAttributeValue]);

  useEffect(() => {
    let configInfo = getProductConfig(
      t,
      productLabel,
      brandData,
      productData,
      selectLabel
    );
    setProductConfig(configInfo);
  }, [productLabel, brandData, productData, selectLabel]);

  useEffect(() => {
    const res = productLabel.find(
      (item) => item.id == addFormik.values.productAttributeId
    );

    setSelectLabel(res?.label);
  }, [addFormik.values.productAttributeId]);

  return (
    <Accourdion
      elevation={0}
      expanded={expandedIndex === 2}
      label={t("events.productFilter")}
      onChange={handleChange(2, [
        "productAttributeId",
        "productAttributeValue",
        "productId",
      ])}
      handlerConfirm={handleConfirm(3, [
        "productAttributeId",
        "productAttributeValue",
        "productId",
      ])}
      handlerCancel={handlerCancel(1, [
        "productAttributeId",
        "productAttributeValue",
        "productId",
      ])}>
      <CmpFormik
        sx={4}
        formik={addFormik}
        formConfig={productConfig}></CmpFormik>
    </Accourdion>
  );
}

export default ProductFilter;
