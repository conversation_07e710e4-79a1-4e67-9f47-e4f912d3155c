export default {
  common: {
    actions: "Actions",
    view: "View",
    edit: "Edit",
    delete: "Delete",
    manage: "Manage",
    preview: "Preview",
    clear: "Clear",
    confirm: "Confirm",
    submit: "Submit",
    next: "Next",
    back: "Back",
    save: "Save",
    reset: "Reset",
    cancel: "Cancel",
    find: "Find",
    refresh: "Refresh",
    resend_email: "ResendEmail",
    upload: "Upload",
    add: "Add",
    prompt: "Prompt",
    ok: "Ok",
    continue: "Continue",
    update: "Update",
    apply: "Apply",
    previous: "Previous",
    type: "Type",
    success: "Success",
    fail: "Fail",
    no_data: "No Data",
    start_time: "Start Time",
    end_time: "End Time",
    per_page: "No of Records Per Page",
    yes: "Yes",
    start_date: "Start Date",
    end_date: "End Date",
    login: "Login",
    password: "Password",
    email: "Email",
    welcome: "Welcome",
    search: "Search",
    enter: "Enter",
    pending: "Pending",
    created: "Created",
    device_deleted: "Device deleted",
    no_options: "No Options",
    system_default: "System Default",
    personal_template: "Personal Template",
    otaUpdata: "OTA Updata",
    is: "Yes",
    is_not: "No",
    common_system_version: "Version",
    common_system_resoltion_recommond:
      "Suggested screen resolution: 1920x1080 or higher",
  },

  dictionary: {
    vertical: "Vertical",
    horizontal: "Horizontal",
    generaluse: "General Use",
    discount: "Discount",
    byunit: "By Unit",
    by_value: "By Value",
    promotion: "Special Promotion",
  },

  system: {
    base_system_browsers: "Browser is recommended for this system",
    base_system_resolution: "Display Resolution",
    base_system_pixels: "Pixels and Above",
    base_system_environment: "Software Running Environment",
    media_personal_about: "About",
  },

  month: {
    january: "January",
    february: "February",
    march: "March",
    April: "April",
    May: "May",
    June: "June",
    July: "July",
    August: "August",
    September: "September",
    October: "October",
    November: "November",
    December: "December",
  },

  zoontime: {
    International: "(GMT-12:00) International Date Line West",
    Coordinated: "(UTC-11)Coordinated Universal Time-11",
    Hawaii: "(UTC-10)Hawaii",
    Alaska: "(UTC-9)Alaska",
    California: "(UTC-8)Pacific time (American and Canada) Baja California",
    Arizona: "(UTC-7)La Paz, The mountain time (American and Canada), Arizona",
    America: "(UTC-6)Saskatchewan, Central time, Central America",
    Eastern:
      "(UTC-5)Bogota, Lima, Quito, Rio Branco, Eastern time, Indiana(East)",
    Caracas: "(UTC-4:30)Caracas",
    Atlantic: "(UTC-4)Atlantic time, Cuiaba, Georgetown, La Paz, Santiago",
    Newfoundland: "(UTC-3:30)Newfoundland",
    Brasilia: "(UTC-3)Brasilia, Buenos Aires, Greenland, Cayenne",
    International2: "(UTC-2)The International Date Line West-02",
    Azores: "(UTC-1)Cape Verde Islands, Azores",
    Edinburgh:
      "(UTC)Dublin, Edinburgh, Lisbon, London, The International Date Line West",
    Brussels: "(UTC+1)Amsterdam, Brussels, Sarajevo",
    Damascus:
      "(UTC+2)Beirut, Damascus, Eastern Europe, Cairo,Athens, Jerusalem",
    Kuwait: "(UTC+3)Baghdad, Kuwait, Moscow, St Petersburg,Nairobi",
    Tehran: "(UTC+3:30)Teheran or Tehran",
    Yerevan: "(UTC+4)Abu Dhabi, Yerevan, Baku, Port Louis, Samarra",
    Kabul: "(UTC+4:30)Kabul",
    Karachi: "(UTC+5)Ashgabat, Islamabad, Karachi",
    Calcutta: "(UTC+5:30)Chennai, Calcutta Mumbai, New Delhi",
    Kathmandu: "(UTC+5:45)Kathmandu",
    Novosibirsk: "(UTC+6)Astana, Dhaka, Novosibirsk",
    Yangon: "(UTC+6:30)Yangon",
    Jakarta: "(UTC+7)Bangkok, Hanoi, Jakarta",
    Beijing:
      "(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi,Kuala Lumpur, Singapore",
    Yakutsk: "(UTC+9)Osaka, Tokyo, Seoul, Yakutsk",
    Adelaide: "(UTC+9:30)Adelaide, Darwin",
    Canberra: "(UTC+10)Brisbane, Vladivostok, Guam, Canberra",
    Islands: "(UTC+11)Solomon Islands, New Caledonia",
    Oakland: "(UTC+12)Anadyr, Oakland, Wellington, Fiji",
    alofa: "(UTC+13)Nuku'alofa, The Samoa Islands",
    Island: "(UTC+14)Christmas Island",
  },
};
