// ==============================|| PRESET THEME - THEME SELECTOR ||============================== //

const Theme = (colors) => {
    const { blue, red, gold, cyan, green, grey } = colors;
    const zkGreen = ['#f9fff0', '#eef5e6', '#d6e8c1', '#b7db93', '#7ac143', '#5a9c2f', '#5a9c2f', '#3e751d', '#254f10', '#122908'];
    const zkRed = ['#fff2f0', '#fff1f0', '#ffccc7', '#ffa39e', '#ff7875', '#ff4d4f', '#d9363e', '#b32430', '#8c1523', '#660e1b'];
    const zkGray = ['#ffffff', '#fafafa', '#f5f5f5', '#f0f0f0', '#d9d9d9', '#bfbfbf', '#8c8c8c', '#595959', '#434343', '#262626'];
    const greyColors = {
        0: grey[0],
        50: grey[1],
        100: grey[2],
        200: grey[3],
        300: grey[4],
        400: grey[5],
        500: grey[6],
        600: grey[7],
        700: grey[8],
        800: grey[9],
        900: grey[10],
        A50: grey[15],
        A100: grey[11],
        A200: grey[12],
        A400: grey[13],
        A700: grey[14],
        A800: grey[16]
    };
    const contrastText = '#fff';

    return {
        primary: {
            lighter: zkGreen[0],
            100: zkGreen[1],
            200: zkGreen[2],
            light: zkGreen[3],
            400: zkGreen[4],
            main: zkGreen[4],
            dark: zkGreen[5],
            700: zkGreen[6],
            darker: zkGreen[7],
            900: zkGreen[8],
            contrastText
        },
        secondary: {
            lighter: greyColors[100],
            100: greyColors[100],
            200: greyColors[200],
            light: greyColors[300],
            400: greyColors[400],
            main: greyColors[500],
            600: greyColors[600],
            dark: greyColors[700],
            800: greyColors[800],
            darker: greyColors[900],
            A100: greyColors[0],
            A200: greyColors.A400,
            A300: greyColors.A700,
            contrastText: greyColors[0]
        },
        error: {
            lighter: red[0],
            light: red[2],
            main: red[4],
            dark: red[7],
            darker: red[9],
            contrastText
        },
        warning: {
            lighter: gold[0],
            light: gold[3],
            main: gold[5],
            dark: gold[7],
            darker: gold[9],
            contrastText: greyColors[100]
        },
        info: {
            lighter: zkGray[0],
            light: zkGray[3],
            main: zkGray[7],
            dark: zkGray[9],
            darker: zkGray[10],
            contrastText: zkGray[0]
        },
        success: {
            lighter: green[0],
            light: green[3],
            main: green[5],
            dark: green[7],
            darker: green[9],
            contrastText
        },
        grey: greyColors
    };
};

export default Theme;
