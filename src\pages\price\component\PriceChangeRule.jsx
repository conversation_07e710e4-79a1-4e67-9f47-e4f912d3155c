import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Grid,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import CustomInput from "../../../components/CustomInput";

export default function PriceChangeRule(props) {
  const { t } = useTranslation();

  const {
    isChangeRuleExpanded,
    handleToggleChangeRuleAccordion,
    promotionType,
    setPromotionType,
    setPayload,
    payload,
    setError,
    error,
    handleToggleTemplateAccordion,
    handleToggleChangeRulePrevious,
    handleChange,
  } = props;

  const promotionTypeOptions = [
    "Discount",
    "By Unit",
    "By Value",
    "Special Promotion",
  ];

  const handlePriceChangeRuleChangeCancel = () => {
    setPromotionType("");
  };

  return (
    <Accordion
      elevation={0}
      style={{ cursor: "pointer" }}
      expanded={isChangeRuleExpanded}
      onChange={handleToggleChangeRuleAccordion}>
      <AccordionSummary
        expandIcon={<GridExpandMoreIcon />}
        aria-controls="panel3-content"
        id="panel3-header"
        sx={{ fontFamily: "Roboto" }}>
        {t("events.priceChangeRule")}
      </AccordionSummary>
      <AccordionDetails>
        <Grid container columnSpacing={1}>
          <Grid item lg={4}>
            <Typography
              sx={{
                fontSize: "12px",
                color: "#474B4F",
                opacity: "0.8",
                paddingBottom: "8px",
              }}>
              {t("events.promotionType")}
              <span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              noOptionsText={t("LVLGF0011")}
              options={promotionTypeOptions}
              value={promotionType}
              onChange={(e, v) => {
                setPromotionType(v);
                setPayload({ ...payload, promotionType: v });
              }}
              getOptionLabel={(option) => (option ? option : "")}
              renderInput={(params) => <TextField {...params} size="small" />}
            />
          </Grid>
          {promotionType === "Discount" && (
            <Grid item lg={4}>
              <CustomInput
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">%</InputAdornment>
                  ),
                }}
                resetError={() => setError({ ...error, promotionValue: "" })}
                required
                error={error.promotionValue}
                label={t("Discount")}
                size="small"
                name="promotionValue"
                value={payload.promotionValue}
                inputProps={{
                  maxLength: 5,
                }}
                helperText={error.promotionValue}
                validation="numeric-percentage"
                handleChange={handleChange}
              />
            </Grid>
          )}
          {promotionType === "By Unit" && (
            <Grid item lg={4}>
              <CustomInput
                resetError={() => setError({ ...error, promotionValue: "" })}
                required
                error={error.promotionValue}
                label={t("Unit")}
                size="small"
                name="promotionValue"
                value={payload.promotionValue}
                inputProps={{
                  maxLength: 50,
                }}
                helperText={error.promotionValue}
                // validation="alpha-numeric"
                handleChange={handleChange}
              />
            </Grid>
          )}
          {promotionType === "By Value" && (
            <Grid item lg={4}>
              <CustomInput
                resetError={() => setError({ ...error, promotionValue: "" })}
                required
                error={error.promotionValue}
                label={t("value")}
                size="small"
                name="promotionValue"
                value={payload.promotionValue}
                inputProps={{
                  maxLength: 12,
                }}
                helperText={error.promotionValue}
                // validation="alpha-numeric"
                handleChange={handleChange}
              />
            </Grid>
          )}
          {promotionType === "Special Promotion" && (
            <Grid item lg={8}>
              <CustomInput
                resetError={() => setError({ ...error, promotionValue: "" })}
                required
                error={error.promotionValue}
                label={t("Promotion")}
                size="small"
                name="promotionValue"
                value={payload.promotionValue}
                inputProps={{
                  maxLength: 12,
                }}
                helperText={error.promotionValue}
                // validation="alpha-numeric"
                handleChange={handleChange}
              />
            </Grid>
          )}
        </Grid>
      </AccordionDetails>
      <Grid container spacing={2} pb={2} pr={2}>
        <Grid item xs={12}>
          <Box display={"flex"} flexDirection={"row-reverse"}>
            <Box item pl={2}>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="contained"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={handleToggleTemplateAccordion}>
                {t("common.next")}
              </Button>
            </Box>
            <Box item pl={2}>
              <Button
                id="AddAuthorizationLevel-button-02"
                className="text-transform-none"
                variant="outlined"
                onClick={handleToggleChangeRulePrevious}
                size="large">
                {t("Previous")}
              </Button>
            </Box>
            <Box item>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="none"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  color: "#1487CA",
                }}
                onClick={handlePriceChangeRuleChangeCancel}>
                {t("common.previous")}
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Accordion>
  );
}
