/**
 * 语言切换功能测试
 * 用于验证子应用语言切换是否正常工作
 */

// 模拟测试环境
const mockLocalStorage = {
  data: {},
  getItem(key) {
    return this.data[key] || null;
  },
  setItem(key, value) {
    this.data[key] = value;
  },
  removeItem(key) {
    delete this.data[key];
  }
};

// 模拟全局对象
global.localStorage = mockLocalStorage;

// 模拟 qiankun props
const mockQiankunProps = {
  language: 'en',
  actions: {
    setGlobalState: jest.fn(),
    onGlobalStateChange: jest.fn()
  }
};

// 模拟 i18n 实例
const mockI18n = {
  changeLanguage: jest.fn()
};

// 导入要测试的函数
import { initSubAppI18n, syncLanguage } from '../util/microAppI18nSync';

describe('语言切换功能测试', () => {
  beforeEach(() => {
    // 清理 localStorage
    mockLocalStorage.data = {};
    // 重置 mock 函数
    jest.clearAllMocks();
  });

  test('初始化时应该使用主应用传递的语言', () => {
    const result = initSubAppI18n(mockI18n, mockQiankunProps);
    
    expect(mockI18n.changeLanguage).toHaveBeenCalledWith('en');
    expect(mockLocalStorage.getItem('zkdigimax_sd_lang')).toBe('en');
    expect(result.currentLanguage).toBe('en');
  });

  test('用户切换语言后刷新应该保持用户选择的语言', () => {
    // 模拟用户切换语言
    syncLanguage(mockI18n, 'zh');
    
    // 验证时间戳被设置
    expect(mockLocalStorage.getItem('lastLanguageChangeTime')).toBeTruthy();
    
    // 模拟页面刷新后的初始化
    const result = initSubAppI18n(mockI18n, mockQiankunProps);
    
    // 应该使用用户选择的语言而不是主应用的语言
    expect(result.currentLanguage).toBe('zh');
  });

  test('时间戳过期后应该使用主应用语言', () => {
    // 设置一个过期的时间戳
    const expiredTime = Date.now() - 10000; // 10秒前
    mockLocalStorage.setItem('lastLanguageChangeTime', expiredTime.toString());
    mockLocalStorage.setItem('zkdigimax_sd_lang', 'zh');
    
    const result = initSubAppI18n(mockI18n, mockQiankunProps);
    
    // 应该使用主应用的语言
    expect(result.currentLanguage).toBe('en');
    // 过期的时间戳应该被清理
    expect(mockLocalStorage.getItem('lastLanguageChangeTime')).toBeNull();
  });

  test('syncLanguage 应该通知主应用语言变更', () => {
    // 设置全局 props
    global.window = { qiankunProps: mockQiankunProps };
    
    syncLanguage(mockI18n, 'zh');
    
    expect(mockQiankunProps.actions.setGlobalState).toHaveBeenCalledWith({ language: 'zh' });
    expect(mockI18n.changeLanguage).toHaveBeenCalledWith('zh');
    expect(mockLocalStorage.getItem('zkdigimax_sd_lang')).toBe('zh');
  });
});

console.log('语言切换测试文件已创建');
