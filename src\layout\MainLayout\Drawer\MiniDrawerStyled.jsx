import React from 'react'
// material-ui
import { styled } from "@mui/material/styles";
import Drawer from "@mui/material/Drawer";

// project import
import { drawerWidth } from "@/config/config";
import { tr } from "date-fns/locale";

const openedMixin = (theme) => ({
  width: drawerWidth,
  // background: "linear-gradient(to bottom, #3498db, #2ecc71)",

  // border: `1px solid ${theme.palette.divider}`,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  // borderRadius: "10px",
  // boxShadow: "0 0 0 5px #00ff00, 0 0 0 10px #ff0000",
  // background: "linear-gradient(to bottom, #00ff00, #ff0000)",
  // backgroundClip: "padding-box",
  // padding: "2px",
  // background: "linear-gradient(to bottom, #3498db, #2ecc71)",
  // border: "5px solid #e0e0e0",
  // background: "transparent",
  height: "90vh",
  borderRadius: "8px",
  margin: "75px 0px 0px 10px",
  overflowX: "hidden",
  boxShadow: "none",
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  height: "100vh",
  // background: "linear-gradient(to bottom, #3498db, #2ecc71)",
  background: "transparent",
  overflowX: "hidden",
  width: 0,
  borderRight: "none",
  boxShadow: theme.customShadows.z1,
});

// ==============================|| DRAWER - MINI STYLED ||============================== //

const MiniDrawerStyled = styled(Drawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme, open }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",

  ...(open && {
    ...openedMixin(theme),
    "& .MuiDrawer-paper": {
      ...openedMixin(theme),
    },
  }),
  ...(!open && {
    ...closedMixin(theme),
    "& .MuiDrawer-paper": closedMixin(theme),
  }),
}));

export default MiniDrawerStyled;
