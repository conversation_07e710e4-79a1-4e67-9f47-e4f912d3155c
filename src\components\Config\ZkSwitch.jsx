import { Form<PERSON><PERSON>perText, Input<PERSON><PERSON>l, Stack, Switch } from "@mui/material";
import RequirePoint from "../RequirePoint";
const ZkInput = (props) => {
  const {
    formik = null,
    placeholder = "",
    handleBlur,
    handleChange,
    label,
    name,
    error,
    labelpostion,
    valueConfig = {
      trueValue: true,
      falseValue: false,
    },
    ...orther
  } = props;

  const changeFn = (e) => {
    let selectValue = e.target.checked
      ? valueConfig.trueValue
      : valueConfig.falseValue;
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    formik.setFieldValue(name, selectValue);
    if (handleBlur) {
      handleBlur(e);
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        direction={labelpostion === "left" ? "row" : "column"}
        spacing={1}>
        <InputLabel
          style={{
            marginTop: labelpostion === "left" ? "12px" : "",
            color: "#474b4fcc",
          }}
          htmlFor={"zkswitch_" + name}>
          {label} {props.required && <RequirePoint></RequirePoint>}
        </InputLabel>
        <Stack
          sx={{
            flexGrow: 1,
            marginTop: labelpostion === "left" ? "12px !important" : "",
            width: "100%",
          }}>
          <Switch
            id={"zkswitch_" + name}
            checked={formik?.values[name] === valueConfig.trueValue}
            onChange={changeFn}
            inputProps={{ "aria-label": "controlled" }}
          />
          {((formik?.touched[name] && formik?.errors[name]) || error) && (
            <FormHelperText error id={`standard-weight-helper-text-${name}`}>
              {formik?.errors[name] || error}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default ZkInput;
