// types
import { createSlice } from "@reduxjs/toolkit";

// initial state
const initialState = {
  menuList: [],
  openItem: ["directory_nutag_dashboard"],
  openComponent: "buttons",
  drawerOpen: false,
  componentDrawerOpen: true,
  backdropOpen: false,
};

// ==============================|| SLICE - MENU ||============================== //

const menu = createSlice({
  name: "menu",
  initialState,
  reducers: {
    activeItem(state, action) {
      state.openItem = action.payload.openItem;
    },

    activeComponent(state, action) {
      state.openComponent = action.payload.openComponent;
    },

    openDrawer(state, action) {
      state.drawerOpen = action.payload.drawerOpen;
    },

    openComponentDrawer(state, action) {
      state.componentDrawerOpen = action.payload.componentDrawerOpen;
    },
    updateBackdropOpen(state, action) {
      state.backdropOpen = action.payload;
    },
    setMenuList(state, action) {
      state.menuList = action.payload;
    },
  },
});

export default menu.reducer;

export const {
  activeItem,
  activeComponent,
  openDrawer,
  openComponentDrawer,
  updateBackdropOpen,
  setMenuList,
} = menu.actions;
