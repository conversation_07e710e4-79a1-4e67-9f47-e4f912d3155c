export default {
  product: {
    "BindProductToScreen.036581-0": "生成图片异常",
    "PreView.474740-0": "正在生成图片，请等待",
    "PreView.474740-1": "图片生成异常",
  },
  tips: {
    no_options: "没有选项",
    search_by_name: "按名称搜索",
    required: "此字段必填",
    invalid_email_address: "无效的邮件地址",
    required_field: "是必填的",
    mobile_number: "手机号码（包括国家代码）必须介于 8 到 23 位数字之间",
    alphabets: "仅接受字母、数字",
    numerics_and_hyphen: "仅接受字母、数字和连字符",
    numerics_and_space: "仅接受字母、数字和空格",
    country_code_required: "国家代码为必填项",
    password_title:
      "为了保证密码安全，密码必须包含：大写字母、特殊符号、数字。例如：C89$Mp@q",
    numerics_and_underscore: "仅接受字母、数字和下划线",
    dot_and_space: "仅接受字母、数字、空格和点",
    underscore_and_space: "仅接受字母、数字、空格和下划线",
    selected_delete_record: "您确定要删除选定的记录吗？",
    occurred: "发生错误",
    no_record_found: "未找到记录。",
    no_of_records_rer_page: "每页记录数",
    select_price_period: "请选择价格变动周期",

    outlet_code: "请输入门店编码",
    country_name: "请输入国家名称",
    state_name: "请输入省份名称",
    city_name: "请输入城市名称",
    city_zone: "请输入城市区域",
    address: "请输入地址",
    area_code: "请输入区域编码",
    upload_size: "图片不能大于3M",
    enter_number: "请输入号码",
    search_retail_name: "按零售商名称搜索",
    suggest_screen: "建议的屏幕分辨率",
    or_higher: "或更高",
    repeat_start: "开始重复时间不能为空",
    endTime_empty: "结束重复时间不能为空",
    promotion_not: "促销结束时间不能早于促销开始时间",
    promotion_shall: "促销结束时间不得早于促销开始时间",
    week_no_empty: "星期不能为空",
    month_no: "月份不能为空",
    date_no: "日期不能为空",
    year_no: "年份不能为空",
    overlap: "促销时间与重复时间没有重叠",
    repetition_time: "促销时间与重复时间没有重叠",
    select_template: "请选择一个模板",
  },

  common_tips: {
    country_code: "请输入国家代码",
    mobile_number: "请输入手机号码",
    first_name: "请输入名字",
    last_name: "请输入姓氏",
    address: "请输入地址",
    country_name: "请输入国家名称",
    state_name: "请输入省份名称",
    city_name: "请输入城市名称",
    start_date: "请选择开始日期",
    end_date: "请选择结束日期",
    operate_fail: "操作失败",
  },

  tips_retail_client: {
    name: "请输入零售商名称",
    code: "请输入零售商编码",
    email: "请输入零售商电子邮箱",
    devices: "请输入设备数量",
    mobile_required: "手机号码为必填项",

    send_activation: "您确定要发送激活邮件吗？",
    activation_success: "激活邮件已成功发送",
    created_success: "零售商创建成功",
    updated_success: "零售商更新成功",
    deleted_success: "零售商删除成功",
    user_exist: "用户已存在。",
    given_name: "该零售商名称已存在。",
    given_code: "该零售商编码已存在。",

    subscription_start_date_invalid: "日期无效，请选择订阅开始日期",
    subscription_expire_date_invalid: "日期无效，请选择订阅到期日期",
  },

  tips_Template: {
    name: "请输入模板名称",
    characters: "名称应少于 51 个字符。",
    screen_resolution: "屏幕分辨率无效",
    success: "模板创建成功",
    updated: "模板更新成功",
    deleted: "模板删除成功",
    not_deleted: "默认模板无法删除",
    sure_delete: "您确定要删除选定的组件吗？",
    can_not_deleted: "模板已和价签同步，无法删除",
    template_exist: "模板名称已存在",
  },

  tips_editor: {
    qrCodeColor: "前景色",
    is_exists: "该元素已存在",
    height_than: "高度不应大于 ",
    width_than: "宽度不应大于 ",
    cannot_add_shape: "当图像与画布大小相等时无法添加形状。",
    cannot_add_text: "当图像与画布大小相等时无法添加文本标签。",
    already_added: "已添加。",

    cannot_add_image: "当图像与画布大小相同时无法添加图像。",
    image_equal: "图像具有相等的高度和宽度但画布已包含对象。",
    adjusted_exceeds: "调整后的宽度或高度超出了画布的宽度和高度。",
    canvas_is_empty: "画布是空的。请添加一些标签或图像。",
    provide_image: "请提供图片给模板",
    text_price: "我是一段文字",
    dimensions_height: "高度不应超过画布尺寸",
    dimensions_width: "宽度不应超过画布尺寸",
  },

  tips_openAPI: {
    name: "请输入名称",
    company_code: "请输入公司编码",
    company_name: "请输入公司名称",
    email: "请输入电子邮箱",
    email_exist: "请确认电子邮箱已存在。",
  },

  tips_picture: {
    id: "请输入图片编号",
    name: "请输入图片名称",
    image_resolution: "图片分辨率必须低于 400x300 像素",
    file_size: "文件大小不能超过 2 MB",
    upload_type: "仅支持上传 .png、.jpeg、.jpg",
    character_50: "图片名称不能超过 50 个字符",
    character_100: "图片编号不能超过 100 个字符",
    character_name: "名字不能超过 100 个字符",
    pictureId_exist: "图片编号已存在",
    upload_image: "请上传图片",
    image_size: "图片大小应小于 2MB",
    support_size: "支持 .jpeg、.jpg、.png",
    resolution_size: "分辨率应低于400*300",
    success: "图片创建成功",
    updated: "图片更新成功",
    deleted: "图片删除成功",
  },

  tips_dashboard: {
    than_start_date: "截至日期应晚于开始日期",
    than_from_date: "截至日期应大于起始日期",
    period_date: "请选择周期",
    period_from_date: "请选择周期的起始日期",
  },

  tips_authorization: {
    name: "输入权限组名称",
    requires_name: "名称为必填项",
    requires_permission: "需要许可",
    role_client_user: "与零售商用户关联的角色",
    deleted: "权限删除成功",
    created: "权限创建成功",
    updated: "权限更新成功",
  },

  tips_product: {
    cannot_deleted: "标签已绑定到产品，无法删除！",
    last_deleted: "最后一个标签无法删除。",
    deleted_product: "产品标签删除成功",
    added_product: "产品标签添加成功",
    field_empty: "此字段为空",

    product_name: "请输入产品名称",
    product_sku: "请输入产品最小存货单位",
    category_level_1: "请输入类别1",
    category_level_2: "请输入类别2",
    category_level_3: "请输入类别3",
    quantity: "请输入数量",
    barcode: "请输入条形码",
    product_price: "请输入产品价格",
    discount_price: "请输入折扣价格",
    brand_name: "请输入品牌名称",
    origin_of_product: "请输入产品产地",
    manufacture: "请输入制造商",
    qr_code: "请输入二维码",

    choose_excel_file: "选择一个 Excel 文件来上传产品",
    note_excel_format: "注意：仅支持 Xls 和 Xlsx 格式",
    download_excel_format: "下载产品文件模板",
    add_product_excel: "从 Excel 文件添加产品",
    upload_picture: "点击此处上传图片",
    country_name: "请输入国家名称",

    name_character_limit: "名称应少于 51 个字符。",
    file_size_limit: "文件大小不能超过 3 MB",
    upload_image_format: "（仅支持上传 Png、Jpeg、Jpg）",
    quantity_character_limit: "数量不能超过6个字符",
    category_1_character_limit: "类别1应少于 51 个字符。",
    category_2_character_limit: "类别2应少于 51 个字符。",
    category_3_character_limit: "类别3应少于 51 个字符。",
    manufacturer_character_limit: "制造商应少于 51 个字符。",
    origin_character_limit: "产品产地应少于 51 个字符。",
    name_required: "名称是必填项",
    product_exists: "该产品名称已存在",
    name_character_limit_50: "名称应少于 50 个字符。",
    name_special_characters: "名称不应包含特殊字符。",
    drag_and_drop_file: "将文件拖放到此处或选择文件",
    upload_file_format: "（上传.png.jpg）",

    product_deleted_success: "产品删除成功",
    product_created_success: "产品创建成功",
    accept_excel_format: "仅接受 Excel 文件格式！",
    product_updated_success: "产品更新成功",
    product_change_confirmation:
      "对产品进行更改会同时更改相关的价格标签和更改事件。<br>您确定要更改产品吗？",
    product_delete_confirmation:
      "同步删除商品会删除相关的价格变动事件和同步日志。",
    sure_change_product:
      "对产品进行更改会同时更改相关的价格标签和更改事件。<br>您确定要更改产品吗？",
    image_format_incorrect: "图片格式不正确。",
  },

  tips_outlet: {
    outlet_name: "请输入门店名称",
    outlet_given_name: "该门店名称已存在",
    outlet_given_code: "该门店编码已存在",
    name_length: "名称应少于 50 个字符。",
    code_length: "编码应少于 30 个字符。",
    code_alphanumeric: "代码仅包含字母、数字。",
    code_exceed: "编码不能超过 30 个字符。",
    outlet_delete_restricted: "无法删除已经添加设备的门店",
    outlet_deleted: "门店删除成功。",
    outlet_created: "门店创建成功。",
    outlet_updated: "门店更新成功。",
    select_outlet: "请选择门店",
  },

  tips_user: {
    outlet_in_charge: "负责门店",
    permission_group_name: "权限组名称",
    date_of_joining: "加入日期",
    first_name: "请输入名字",
    last_name: "请输入姓氏",
    person_id: "请输入人员编号",
    enter_email: "请输入电子邮箱",
    select_date: "DD-MM-YYYY（请选择日期）",
    first_name_limit: "名字不能超过 100 个字符",
    allowed_file_types: "仅允许 *JPEG、*JPG、*PNG",
    max_file_size: "最大尺寸为 3 MB",
    user_exists: "用户已存在。",
    phone_required: "电话为必填项",
    outlet_required: "门店为必填项",
    role_required: "规则为必填项",
    activation_mail_confirmation: "您确定要发送激活邮件吗？",
    activation_mail_sent: "激活邮件已成功发送",
    cannot_delete_default_user: "默认用户无法删除。",
    person_deleted: "用户删除成功",
    person_created: "用户创建成功",
    person_updated: "用户更新成功",
  },

  tips_gateway: {
    deviceNetworkConfigSteps: "设备网络配置步骤",
    step1PowerUp: "步骤 1：打开电源并打开设备",
    step1Description: "如果设备有网络，它将自动连接网络并开始工作。",
    step2ConfigureNetwork: "第 2 步：配置网络",
    step2Description:
      "您可以使用蓝牙来设置网络。或者某些设备具有触摸屏，其固件中有网络设置。",
    step3RestoreFactorySettings: "步骤3：恢复出厂设置",
    step3Description: "有些设备无法进行网络设置。您可以尝试重置设备出厂设置。",
    readInstructions: "我已阅读这些说明",

    manualRegisterDevice: "手动注册设备",
    powerUpAndSetNetwork: "启动并设置设备网络",
    step1EthernetConnection: "1. 如果设备支持以太网功能，请插入网线。",
    step2CommunicationSetting:
      "2. 进入设备以太网设置/WiFi 设置菜单，进入通讯设置页面。网络设置成功，设备将在待机页面显示二维码。",
    step3FindSerialNumber: "3. 在设备盒的侧面或设备背面，可以找到设备序列号。",
    step4FillSerialNumber: "4.在系统上填写设备序列号。",

    specifyDeviceForClient: "请将设备添加到零售商门店。",
    syncOutletTimeZone: "该设备将同步与门店相同的时区。",
    device_does_not: "设备不存在.",
    selectFile: "请选择文件",
    versionStartWithV: "必须以字母 V 开头",
    enterUpgradePackageVersion: "请输入升级包版本，以字母‘V’开头",
    uploadSuccessful: "上传成功",
    uploadAndUpgrade: "上传升级包并升级",
    uploadFile: "上传文件",
    unsupportedType: "当前类型不支持",
    fileSizeLimit:
      "文件大小超出限制：{{fileSize}}MB。请上传小于或等于 100MB 的文件。",
    dragOrBrowse: "拖拽或浏览",
    onlyApkFormat: "仅允许上传 apk 格式的文件",

    searchByDeviceSn: "按设备序列号搜索",
    selectYourZone: "请选择您的区域",
    bindZone: "绑定区域",
    enterDeviceSerialNumber: "请输入设备序列号",
    readInstructionsToAddDevice: "请阅读说明并选中复选框以添加设备。",

    deviceUnbindSuccess: "设备解除绑定成功",
    deviceBindSuccess: "设备绑定成功",
    upload_excel_file: "请上传excel文件",
    device_has_been_bound: "设备已被绑定",
  },

  tips_screens: {
    choose_Product_display: "选择想要此屏幕显示的产品",
    choose_Template_display: "选择想要此屏幕显示的模板",
    selected_screen: "选择屏幕",
    search_by_screenId: "按屏幕编号搜索",
    enter_screen_name: "请输入屏幕名称",
    enter_position_no: "请输入位置号",
    enter_gateway_sn: "请输入网关序列号",
    template_not_empty: "模板不能为空",
    product_not_empty: "产品不能为空",
    company_id_required: "公司编号为必填项",
    select_one_screen: "请选择屏幕",
    confirm_unbind_device: "您确定要解除选定设备的绑定吗？",
    deviceUn_bind_success: "设备解除绑定成功",
    screens_sync_success: "屏幕同步成功",
    select_promotion_StartTime: "请选择促销活动的开始时间",
    select_or_screen: "请选择一个或多个屏幕",
    batch_update_screen_success: "批量编辑屏幕成功",
  },

  tips_events: {
    deleted: "价格变动事件删除成功",
    created: "价格变动事件创建成功",
    updated: "价格变动事件更新成功",
  },

  tips_approval: {
    change_status: "确定要改变状态吗？",
    updated: "记录更新成功",
    not_empty: "请选择一个唯一值，且不能为空。",
    deleted: "审批流程删除成功",
    added: "审批流程添加成功",
    information: "任何一位审批人批准，系统就会将信息同步到屏幕。",
  },

  tips_mobile: {
    mobile_api_management: "移动应用程序管理",
    app_name: "应用程序名称",
    app_version_type: "应用版本类型",
    app_mobile_version: "应用程序移动版本",
    is_there_any_update: "是否有更新",
    is_it_a_force_update: "强制更新",
    version_name: "版本名称",
    version_number: "版本号",
    update_content: "更新内容",
    update_address: "更新地址",
    app_version_type: "应用程序版本类型",
    app_name_max_length: "应用名称不能超过 50 个字符。",
    app_name_required: "应用名称为必填项。",
    type_required: "类型为必填项。",
    version_number_required: "版本号为必填项。",
    invalid_version_number: "版本号无效。",
    have_update_required: "需要更新。",
    force_update_required: "需要强制更新。",
    update_content_required: "需要更新内容。",
    update_address_required: "需要更新地址。",
    successfully_created_app: "成功创建移动应用。",
    adequate_privileges: "权限不足。",
    add_new_app: "添加新应用",
    app_name_label: "应用名称",
    app_name_placeholder: "请输入应用名称",
    type_label: "类型",
    type_placeholder: "选择类型",
    version_number_label: "版本号",
    version_number_placeholder: "请输入版本号",
    have_update_label: "是否有更新",
    have_update_placeholder: "选择有更新",
    force_update_label: "强制更新",
    force_update_placeholder: "选择强制更新",
    update_content_label: "更新内容",
    update_content_placeholder: "请输入更新内容",
    update_address_label: "更新地址",
    update_address_placeholder: "请输入更新地址",
    delete_mobile: "删除应用!",
    sure_detele: "您确定要永久删除此移动应用程序吗?",
    Android: "安卓",
    Ios: "IOS",
    Force_Update_required: "必需",
    Force_Update_not_required: "不需要",
    Any_Update_Yes: "是",
    Any_Update_No: "否",
    Mobile_version_deleted_successfully: "移动版本删除成功",
    Delete_conformation: "删除确认",
    Enter_the_name: "输入名称",
  },
};
