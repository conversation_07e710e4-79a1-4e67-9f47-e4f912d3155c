import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Box } from "@mui/material";
import PieSvg from "./PIe";
function ESL(props) {
  const { dashBoardData } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Box>
        <Grid
          container
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
          }}>
          <Grid item>
            <Typography variant="h6" fontWeight={"bold"}>
              {t("dashboard.gateway_online_status")}
            </Typography>
          </Grid>
          <Grid item pt={2}>
            <div
              style={{
                position: "relative",
                width: "160px",
                height: "160px",
                marginBottom: "20px",
              }}>
              <PieSvg
                value={
                  dashBoardData?.onlinePercentage
                    ? dashBoardData?.onlinePercentage
                    : 0
                }></PieSvg>
            </div>
          </Grid>
        </Grid>
      </Box>
    </React.Fragment>
  );
}

export default ESL;
