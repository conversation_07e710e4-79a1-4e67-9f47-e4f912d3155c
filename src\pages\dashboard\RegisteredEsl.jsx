import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Box } from "@mui/material";
import EpriceGateway from "@/assets/images/eprice_registered_gateway.svg?react";

function RegisteredEsl(props) {
  const { dashBoardData } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Grid
        alignItems={"center"}
        sx={{
          display: "flex",
          flexDirection: "row",
          height: "100%",
          width: "100%",
        }}
        columnSpacing={1}>
        <Grid xs={6}>
          <EpriceGateway style={{ width: "100%" }} />
        </Grid>
        <Grid
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "flex-start",
          }}>
          <Typography style={{ color: "gray", fontSize: "16px" }}>
            {t("dashboard.registered_gateway")}
          </Typography>
          <Typography
            style={{
              fontSize: "38px",
              fontWeight: "bold",
              textAlign: "center",
            }}>
            {dashBoardData?.deviceCountOfOutlet
              ? dashBoardData?.deviceCountOfOutlet
              : 0}
          </Typography>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default RegisteredEsl;
