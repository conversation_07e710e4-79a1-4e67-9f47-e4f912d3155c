import * as Yup from "yup";
export const createValidation = (config, expendObj = {}) => {
  let shapeObj = {};

  config.forEach((item) => {
    if (item?.required) {
      let validation = item.validation;
      if (
        validation &&
        (item.conditionalRendering === undefined ||
          (item.conditionalRendering && item.conditionalRendering()))
      ) {
        let temp = Yup;
        validation.forEach((valid) => {
          let type = valid.type;

          if (type === "string" || type === "required") {
            temp = temp[type](valid.message);
          } else if (type === "number") {
            temp = temp?.matches(/^\d+(\.\d+)?$/, {
              message: valid.message,
              excludeEmptyString:
                valid.excludeEmptyString === undefined
                  ? true
                  : valid.excludeEmptyString,
            });
          } else if (type === "positive") {
            temp = temp.positive(valid.message);
          } else if (type === "min" || type === "max") {
            if (valid.value === undefined) {
              console.error("请配置" + temp[type] + "范围的值");
            } else {
              temp = temp[type](valid.value, valid.message);
            }
          } else if (type === "test") {
            temp = temp.test({
              name: valid[type],
              test(value, ctx) {
                return valid.callback(value, ctx);
              },
            });
          } else if (type === "matches") {
            temp = temp.matches(valid.matches, {
              message: valid.message,
              excludeEmptyString:
                valid.excludeEmptyString === undefined
                  ? true
                  : valid.excludeEmptyString,
            });
          } else if (type === "email") {
            temp = temp.email(valid.message);
          } else if (type === "password") {
            temp = temp.matches(/^[^\u4e00-\u9fa5]{8,20}$/, {
              message: valid.message,
            });
          } else if (type === "secondConfirm") {
            temp = temp.oneOf([Yup.ref("password"), null], valid.message);
          } else if (type === "phoneNumber") {
            temp = temp.matches(
              /^\+?1?\s*[-\/.]?\s*\(?\d{3}\)?[-\/.\s]?\d{3}[-\/.]?\d{4}$/,
              {
                message: valid.message,
              }
            );
          }
        });
        shapeObj[item.name] = temp;
      }
    }
  });
  let tempObj = {
    ...shapeObj,
    ...expendObj,
  };

  return Yup.object().shape(tempObj);
};
