import request from "@/util/request";

const baseUrl = "/nt/v1";

/**
 * 获取分辨率列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回请求的Promise对象
 */
export const list = (params) => {
  return request({
    url: `${baseUrl}/resolution`,
    method: "GET",
    params: params,
  });
};

/**
 * 添加新分辨率
 * @param {Object} params - 新分辨率的数据
 * @returns {Promise} 返回请求的Promise对象
 */

export const add = (params) => {
  return request({
    url: `${baseUrl}/resolution`,
    method: "POST",
    data: params,
  });
};

/**
 * 编辑现有分辨率
 * @param {string|number} id - 要编辑的分辨率ID
 * @param {Object} params - 更新的分辨率数据
 * @returns {Promise} 返回请求的Promise对象
 */
export const edit = (id, params) => {
  return request({
    url: `${baseUrl}/resolution/${id}}`,
    method: "PUT",
    data: params,
  });
};

/**
 * 删除分辨率
 * @param {Object} params - 要删除的分辨率数据
 * @returns {Promise} 返回请求的Promise对象
 */
export const deletes = (params) => {
  return request({
    url: `${baseUrl}/resolution`,
    method: "DELETE",
    data: params,
  });
};
