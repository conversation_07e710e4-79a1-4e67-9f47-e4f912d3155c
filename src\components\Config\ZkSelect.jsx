import React, { useEffect } from "react";
/* eslint-disable no-unreachable */
import { forwardRef, useRef } from "react";
import PropTypes from "prop-types";
import { Select, MenuItem, OutlinedInput, IconButton } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import { FixedSizeList } from "react-window";
import RequirePoint from "../RequirePoint";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const ZKSelect = forwardRef((props, ref) => {
  const {
    // 具体传递的对象 格式：[{label:"xx",value:"11"}]
    formik,
    options = [],
    labeloptions = { label: "label", value: "value" },
    // 回调的value
    value = "",
    isClear = true,
    label = "",
    // 表单name
    name = "",
    size = "Normal",
    // 表单校验错误
    error = false,
    placeholder = "Please Select",
    // change事件
    onChange = () => {},
    // 虚拟化列表
    virtualized = false,
    // 清除按钮需要双击生效
    handleBlur,
    handleChange,
    labelpostion,
    // eslint-disable-next-line react/prop-types
    getAddress,
    // eslint-disable-next-line react/prop-types
    menuWidth = 250,
    disabled = false,
    sx,
    ...orther
  } = props;

  const blurFn = (e) => {
    if (formik?.handleBlur) {
      formik?.handleBlur(e);
    }
    if (handleChange) {
      handleChange();
    }
  };

  const changeFn = (e) => {
    if (formik?.handleChange) {
      formik?.handleChange(e);
    }
    if (handleBlur) {
      handleBlur(e);
    }
  };

  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: !virtualized
          ? ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP
          : undefined,
        width: menuWidth,
        "& .MuiSelect-select": {
          height: "40px",
        },
      },
    },
  };
  const selectRef = useRef(null);

  const Row = ({ index, style }) => {
    const item = options[index];
    return (
      <>
        <MenuItem
          style={style}
          key={item[labeloptions.value]}
          value={item[labeloptions.value]?.toString()}
          onClick={(e) => handleItemClick(e, index)}>
          {item[labeloptions.label]}
        </MenuItem>
      </>
    );
  };

  const handleItemClick = (e, index) => {
    const selectedOption = options[index];
    handleChange(selectedOption);
    if (getAddress) {
      getAddress(selectedOption);
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              // color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {props.required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}
        <Stack
          sx={{
            // flexGrow: 1,
            width: "100%",
          }}>
          <Select
            disabled={disabled}
            sx={{
              color: value === "" ? "#757575" : "black",
              ...sx,
              "& .MuiSelect-select": {
                height: "40px",
              },

              "& .MuiSelect-root": {
                height: "40px",
              },
            }}
            MenuProps={MenuProps}
            inputProps={{
              "aria-label": "Without label",
            }}
            ref={selectRef}
            fullWidth
            error={error}
            displayEmpty
            onChange={changeFn}
            onBlur={blurFn}
            name={name}
            size={size}
            value={formik ? formik.values[name] : value}
            input={
              <OutlinedInput
                sx={{
                  "& .MuiInputBase-input": {
                    fontSize: "14px",
                    height: "36px",
                  },
                }}
                error={Boolean(
                  (formik?.touched[name] && formik?.errors[name]) || error
                )}
                endAdornment={
                  isClear
                    ? value && (
                        <IconButton
                          sx={{
                            marginRight: "20px",
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onClear();
                          }}>
                          <ClearIcon
                            fontSize="small"
                            sx={{
                              color: "#757575",
                              cursor: "pointer",
                            }}
                          />
                        </IconButton>
                      )
                    : null
                }
              />
            }
            {...orther}>
            <MenuItem disabled value="">
              <span
                style={{
                  fontSize: "14px",
                  color: "#a4a4a4d1",
                }}>
                {placeholder}
              </span>
            </MenuItem>
            {virtualized && (
              <FixedSizeList
                height={200}
                itemCount={options.length}
                itemSize={40}>
                {Row}
              </FixedSizeList>
            )}

            {!virtualized &&
              options?.map((item) => (
                <MenuItem
                  key={item[labeloptions.value]}
                  value={item[labeloptions.value]?.toString()}>
                  {item[labeloptions.label]}
                </MenuItem>
              ))}
          </Select>
        </Stack>
      </Stack>
    </Stack>
  );
});

ZKSelect.propTypes = {
  label: PropTypes.string,
  labelpostion: PropTypes.string,
  options: PropTypes.array,
  value: PropTypes.any,
  error: PropTypes.bool,
  name: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onClear: PropTypes.func,
  labeloptions: PropTypes.object,
  disabled: PropTypes.bool,
  sx: PropTypes.object,
};
export default ZKSelect;
