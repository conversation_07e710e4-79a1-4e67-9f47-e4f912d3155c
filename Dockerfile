# Use an official Node.js image as the base
FROM node:18-alpine AS build

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# Install dependencies
RUN npm config set registry https://registry.npmmirror.com && yarn

# Copy the rest of the application code to the container
COPY . .

# Set the environment variable for production
ENV NODE_ENV=production

# Build the React application
RUN npm run build

# Use a lightweight Nginx image for the final stage
FROM nginx:alpine

# Copy Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Copy the built application from the previous stage
COPY --from=build /app/dist /usr/share/nginx/html

# Expose the desired port
EXPOSE 8083
