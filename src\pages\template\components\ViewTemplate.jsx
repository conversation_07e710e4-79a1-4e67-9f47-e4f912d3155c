import React from "react";
import RightViewLayout from "@c/RighViewLayout";
import ViewPage from "@c/ViewPage";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { getDetail } from "@s/TemplateService";

function ViewTemplate() {
  const { t } = useTranslation();
  const { state } = useLocation();
  const [payload, setPayload] = useState({
    name: "",
    resolution: "",
    orientation: "",
  });

  useEffect(() => {
    getDetail(state?.id).then((res) => {
      setPayload({
        ...payload,
        ...res.data,
      });
    });
  }, []);

  return (
    <React.Fragment>
      <RightViewLayout
        id="viewtempback"
        navigateBack={"-1"}
        title={t("template.view_template")}>
        <ViewPage>
          <Box mt={2}>
            <Typography variant="fieldLabel">{t("template.name")}:</Typography>
          </Box>
          <Typography variant="fieldValue">
            {payload && payload.name ? payload.name : "-"}
          </Typography>

          <Box mt={2}>
            <Typography variant="fieldLabel">
              {t("template.resolution")}:
            </Typography>
          </Box>
          <Typography variant="fieldValue">
            {payload && payload.resolution ? payload.resolution : "-"}
          </Typography>

          <Box mt={2}>
            <Typography variant="fieldLabel">
              {t("template.screen_direction")}:
            </Typography>
          </Box>
          <Typography variant="fieldValue">
            {payload && payload.orientation ? payload.orientation : "-"}
          </Typography>
        </ViewPage>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default ViewTemplate;
