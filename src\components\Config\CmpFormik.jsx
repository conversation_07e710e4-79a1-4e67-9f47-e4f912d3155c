import { Grid, Button } from "@mui/material";
import RenderingFromItem from "./RenderingFromItem";

import React from "react";
const CmpFormik = (props) => {
  const {
    formik,
    formConfig,
    labelpostion = "top",
    itemSpacing = 2,
    sx = 12,
    children,
  } = props;

  return (
    <form noValidate onSubmit={formik.handleSubmit}>
      <Grid container sx={{ color: "green" }} spacing={itemSpacing}>
        <RenderingFromItem
          sx={sx}
          labelpostion={labelpostion}
          config={formConfig}
          formik={formik}></RenderingFromItem>
        {children}
      </Grid>
    </form>
  );
};

export default CmpFormik;
