import { useEffect, useState } from "react";
import { Box, Grid, FormControlLabel, Checkbox } from "@mui/material";

import { useTranslation } from "react-i18next";
import { handlerMonth, handlerAllCheckMonth } from "../js/way";
function Monthly(props) {
  const { addFormik } = props;
  const { t } = useTranslation();
  const [selectedMonth, setSelectedMonth] = useState([]);
  const [isCheckedMonth, setIsCheckedMonth] = useState(false);

  const allMonth = [
    { id: 1, value: t("month.january") },
    { id: 2, value: t("month.february") },
    { id: 3, value: t("month.march") },
    { id: 4, value: t("month.April") },
    { id: 5, value: t("month.May") },
    { id: 6, value: t("month.June") },
    { id: 7, value: t("month.July") },
    { id: 8, value: t("month.August") },
    { id: 9, value: t("month.September") },
    { id: 10, value: t("month.October") },
    { id: 11, value: t("month.November") },
    { id: 12, value: t("month.December") },
  ];

  useEffect(() => {
    setSelectedMonth(JSON.stringify(addFormik.values.months));
  }, []);

  useEffect(() => {
    // 判断是否全选中
    const isAllSelected = allMonth.every((date) =>
      selectedMonth?.includes(date.id)
    );

    setIsCheckedMonth(isAllSelected);
  }, [selectedMonth]);

  return (
    <Box m={1} width={"60%"} ml={2.5}>
      <Box>
        <FormControlLabel
          control={
            <Checkbox
              checked={isCheckedMonth}
              onChange={(e) =>
                handlerAllCheckMonth(
                  e,
                  setSelectedMonth,
                  setIsCheckedMonth,
                  allMonth,
                  addFormik
                )
              }
            />
          }
          required
          label={t("events.validMonth")}
          sx={{
            textAlign: "left",
            font: "normal normal medium 18px/24px Roboto",
            fontWeight: "bold",
            letterSpacing: "10px",
            color: "red",
          }}
        />
      </Box>

      <Box ml={4} display={"flex"}>
        {allMonth?.map((item, index) => {
          return (
            <Grid container spacing={1} md={10} key={index}>
              <Grid item xs={2} pr={1}>
                <FormControlLabel
                  key={item.id}
                  checked={selectedMonth?.includes(item.id)}
                  control={
                    <Checkbox
                      onChange={(e) => {
                        handlerMonth(
                          item.id,
                          selectedMonth,
                          setSelectedMonth,
                          allMonth,
                          addFormik,
                          setIsCheckedMonth
                        );
                      }}
                    />
                  }
                  label={item?.value}
                  sx={{
                    textAlign: "left",
                    font: "normal normal medium 18px/24px Roboto",
                    fontWeight: "bold",
                    letterSpacing: "30px",
                    color: "#474B4F",
                    whiteSpace: "nowrap",
                  }}
                />
              </Grid>
            </Grid>
          );
        })}
      </Box>
    </Box>
  );
}

export default Monthly;
