import { getImage } from "@s/TemplateService";
import {
  getProduct,
  getProductValue,
  getProductLabel,
  uploadImage,
} from "@/services/common.js";
import { getDetail, OnlineOutlets, getTemplates } from "@s/price";
import dayjs from "dayjs";
import { toast } from "react-toastify";
//获取模板列表接口数据
export const loadDataTemplates = (
  filters,
  setTemplateList = () => {},
  type,
  productId = "",
  setCurrentPage = () => {},
  serchData = null,
  setTotalPages = () => {}
) => {
  getTemplates({
    ...filters,
    productId: productId,
    promotionType: type,
    // productId: "1895308691588984833",
    // promotionType: "1",
    name: serchData,
  }).then((res) => {
    if (res?.code == "00000000") {
      setTemplateList(res?.data?.data);
      setCurrentPage(res?.data?.page);
      setTotalPages(res?.data?.total);
    } else {
      setTemplateList([]);
      setTotalPages(0);
    }
  });
};

// 获取设备在线的门店
export const getOnlineOutlet = (
  filters,
  setTotalRecords = [],
  setRecords,
  zoneId = ""
) => {
  OnlineOutlets({
    ...filters,
    timeZone: zoneId,
  }).then((res) => {
    if (res?.code == "00000000") {
      setTotalRecords(res?.data?.total);
      setRecords(res?.data?.data);
    }
  });
};

// 获取商品标签
export const getProductlabel = (setProductlabel) => {
  getProductLabel().then((res) => {
    if (res?.code == "00000000") {
      setProductlabel(res?.data);
    } else {
      setProductlabel([]);
    }
  });
};

// 根据商品类型 获取当前商品品牌
export const getProductBrandInfo = (elementType, setBrandData) => {
  try {
    getProductValue(elementType).then((res) => {
      if (res?.code == "00000000") {
        setBrandData(res?.data);
      } else {
        setBrandData([]);
      }
    });
  } catch (error) {
    console.error("响应错误,无返回数据");
    setBrandData([]);
  }
};

// 根据商品品牌 获取当前商品数据
export const getProductInfo = (elementType, brandData, setProductData) => {
  getProduct({
    typeId: elementType,
    typeValue: brandData,
  }).then((res) => {
    if (res?.code == "00000000") {
      setProductData(res?.data);
    } else {
      setProductData([]);
    }
  });
};

export const getCurrencySymbol = (currency) => {
  if (currency === "INR") {
    return "\u20B9";
  }
  if (currency === "USD") {
    return "\u0024";
  }
  if (currency === "EU") {
    return "\u20AC";
  }
  if (currency === "RMB") {
    return "\u00A5";
  }
  if (currency === "THB") {
    return "\u0E3F";
  }
};

// 将选中商品数据替换到选张得模板上面
export const updateComponentValues = (
  componentList,
  customPayload,
  addFormik
) => {
  let newJSON = JSON.parse(componentList);
  newJSON?.componentList?.forEach((item) => {
    if (item.type == "priceRule") {
      if (addFormik.values["promotionType"] == 1) {
        item.value = addFormik.values["promotionValue"] + "%";
      } else {
        item.value = addFormik.values["promotionValue"];
      }
    } else {
      if (customPayload?.hasOwnProperty(item.type)) {
        if (item.type === "qrCode" || item.type === "barCode") {
          item.value = customPayload[item.type];
          item.text = customPayload[item.type];
        } else if (
          item.type === "discountPrice" &&
          addFormik.values["promotionType"] == 1
        ) {
          item.value = (
            customPayload.productPrice -
            customPayload.productPrice *
              (addFormik.values["promotionValue"] / 100)
          ).toFixed(2);
        } else {
          item.value = customPayload[item.type];
        }
      }
    }
  });

  return newJSON;
};

export const handlerUpdataFile = (item, addFormik) => {
  return new Promise((resolve, reject) => {
    // Create a temporary container for rendering
    // const tempContainer = document.createElement('div')
    // tempContainer.style.position = 'absolute'
    // tempContainer.style.left = '-9999px'
    // document.body.appendChild(tempContainer)
    const elementJSON = item;
    getImage({
      templateJson: JSON.stringify(elementJSON),
    }).then(async (res) => {
      let data = res?.data || {};
      if (res?.code == "00000000") {
        let dataUrl = data.templateBase64;
        const byteString = atob(dataUrl.split(",")[1]);
        const mimeString = dataUrl.split(",")[0].split(":")[1].split(";")[0];
        const arrayBuffer = new ArrayBuffer(byteString.length);
        const uintArray = new Uint8Array(arrayBuffer);
        for (let i = 0; i < byteString.length; i++) {
          uintArray[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([uintArray], { type: mimeString });
        const productName = localStorage.getItem("productName") || "product";
        const file = new File([blob], `${productName}.png`, {
          type: "image/png",
        });

        blobToDataURI(blob, (result) => {
          console.log("HHHHHHHHHHH", result);
          console.log(
            "%c+",
            `font-size: 1px;
              padding: 100px 100px;
              background-image: url(` +
              result +
              `);
              background-size: contain;
              background-repeat: no-repeat;
              color: transparent;`
          );
        });
        // Create FormData for file upload
        const formData = new FormData();
        formData.append("multipartFile", file);

        // Upload the file to the server
        try {
          const response = await uploadImage(formData);
          debugger;
          if (response?.code == "00000000") {
            const updatedItem = {
              ...item,
              imageUrl: response.data?.url, // Update the item with the new imageUrl
            };
            addFormik.setFieldValue("url", response.data?.url);
            resolve(updatedItem); // Resolve the updated item
          } else {
            console.error("Upload failed");
            addFormik.setFieldValue("url", "");
            reject(new Error("Upload failed"));
          }
        } catch (uploadError) {
          console.error("Error during file upload:", uploadError);
          reject(uploadError);
        }
      } else {
        console.error(new Error("生成图片异常"));
        reject(null);
        return;
      }
    });

    // // Render the PreView component inside the temporary container
    // ReactDOM.render(<PreView layoutJSON={elementJSON} />, tempContainer, () => {
    //   // Ensure the DOM has rendered by introducing a short delay
    //   setTimeout(() => {
    //     const width = parseInt(elementJSON.width, 10);
    //     const height = parseInt(elementJSON.height, 10);

    //     if (!width || !height) {
    //       console.error("Invalid width or height");
    //       reject(new Error("Invalid width or height"));
    //       return;
    //     }

    //     // Create a canvas element for capturing the rendered component
    //     const canvas = document.createElement("canvas");
    //     canvas.width = width;
    //     canvas.height = height;

    //     // Capture the rendered content using html2canvas
    //     html2canvas(tempContainer, {
    //       canvas,
    //       scale: 1,
    //       useCORS: true,
    //       allowTaint: true,
    //     })
    //       .then(async (capturedCanvas) => {
    //         const dataUrl = capturedCanvas.toDataURL("image/png");
    //         const byteString = atob(dataUrl.split(",")[1]);
    //         const mimeString = dataUrl
    //           .split(",")[0]
    //           .split(":")[1]
    //           .split(";")[0];
    //         const arrayBuffer = new ArrayBuffer(byteString.length);
    //         const uintArray = new Uint8Array(arrayBuffer);
    //         for (let i = 0; i < byteString.length; i++) {
    //           uintArray[i] = byteString.charCodeAt(i);
    //         }

    //         const blob = new Blob([uintArray], { type: mimeString });
    //         const productName =
    //           localStorage.getItem("productName") || "product";
    //         const file = new File([blob], `${productName}.png`, {
    //           type: "image/png",
    //         });

    //         blobToDataURI(blob, (result) => {
    //           console.log("HHHHHHHHHHH", result);
    //           console.log(
    //             "%c+",
    //             `font-size: 1px;
    //           padding: 100px 100px;
    //           background-image: url(` +
    //               result +
    //               `);
    //           background-size: contain;
    //           background-repeat: no-repeat;
    //           color: transparent;`
    //           );
    //         });
    //         // Create FormData for file upload
    //         const formData = new FormData();
    //         formData.append("mediaType", "image/png");
    //         formData.append("file", file);

    //         // Upload the file to the server
    //         try {
    //           const response = await axios.post(
    //             `${process.env.REACT_APP_SERVER_URL}/web/uploadFile`,
    //             formData,
    //             {
    //               headers: {
    //                 "Content-Type": "multipart/form-data",
    //                 Authorization: localStorage.getItem("USER_TOKEN"),
    //               },
    //             }
    //           );

    //           if (response?.data?.code === "LVLI0000") {
    //             const updatedItem = {
    //               ...item,
    //               imageUrl: response.data.data, // Update the item with the new imageUrl
    //             };
    //             addFormik.setFieldValue("url", response.data.data);
    //             resolve(updatedItem); // Resolve the updated item
    //           } else {
    //             console.error("Upload failed");
    //             addFormik.setFieldValue("url", "");
    //             reject(new Error("Upload failed"));
    //           }
    //         } catch (uploadError) {
    //           console.error("Error during file upload:", uploadError);
    //           reject(uploadError);
    //         } finally {
    //           // Clean up the temporary container after processing
    //           document.body.removeChild(tempContainer);
    //         }
    //       })
    //       .catch((canvasError) => {
    //         console.error("Error capturing canvas:", canvasError);
    //         reject(canvasError);
    //         // Clean up
    //         document.body.removeChild(tempContainer);
    //       });
    //   }, 100); // Optional delay to ensure rendering completion
    // });
  });
};

export const blobToDataURI = (blob, callback) => {
  var reader = new FileReader();
  reader.readAsDataURL(blob);
  reader.onload = function (e) {
    callback(e.target.result);
  };
};

//获取变价详情
export const getPriceDetail = (id, setDetailData, addFormik) => {
  getDetail(id).then((res) => {
    setDetailData(res?.data);
    // addFormik.setFieldValue("detailList", res?.data);
  });
};

export const handlerView = (
  templateList,
  productData,
  templateId,
  setSelectedImage,
  setLightboxOpen,
  addFormik,
  lightboxOpen
) => {
  const templateData = templateList?.find((itme) => {
    return itme?.id === templateId;
  });

  if (templateData && productData) {
    let elementJSON = updateComponentValues(
      templateData?.templateJson,
      productData,
      addFormik
    );

    getImage({
      templateJson: JSON.stringify(elementJSON),
    }).then(async (res) => {
      let data = res?.data || {};
      if (res.code == "00000000") {
        let dataUrl = data.templateBase64;
        const byteString = atob(dataUrl.split(",")[1]);
        const mimeString = dataUrl.split(",")[0].split(":")[1].split(";")[0];
        const arrayBuffer = new ArrayBuffer(byteString.length);
        const uintArray = new Uint8Array(arrayBuffer);
        for (let i = 0; i < byteString.length; i++) {
          uintArray[i] = byteString.charCodeAt(i);
        }

        const blob = new Blob([uintArray], { type: mimeString });
        const productName = localStorage.getItem("productName") || "product";
        const file = new File([blob], `${productName}.png`, {
          type: "image/png",
        });
        blobToDataURI(blob, (result) => {
          setSelectedImage(result);
          setLightboxOpen(true);
        });
      } else {
        setSelectedImage("");
      }
    });
  }
};

// 获取商品标签 Element Label
export const getProductLabelData = (setProductLabel) => {
  getProductLabel().then((res) => {
    if (res?.code == "00000000") {
      let list = res.data.filter((item) => {
        return !(
          item.dataLevel == "System Default" && item.name == "priceRule"
        );
      });

      setProductLabel(list);
    } else {
      setProductLabel([]);
    }
  });
};

export const validateForm = (updatedValues) => {
  if (!updatedValues?.scheduleMode) {
    enqueueSnackbar("The schedule mode cannot be empty", {
      variant: "error",
    });
    return false;
  }

  let currentTime = dayjs(); // 当前时间

  //  开始促销时间
  let startTime = dayjs(updatedValues?.startDayTime, "YYYY-MM-DD HH:mm:ss").set(
    "second",
    0
  );
  // 结束促销时间
  let endTime = dayjs(updatedValues?.endDayTime, "YYYY-MM-DD HH:mm:ss").set(
    "second",
    59
  );

  if (!startTime.isValid()) {
    enqueueSnackbar("The start promotion time cannot be empty", {
      variant: "error",
    });
    return false;
  }

  const { endOrNot } = updatedValues;

  if (!endOrNot && !endTime.isValid()) {
    enqueueSnackbar("The end promotion time cannot be empty", {
      variant: "error",
    });
    return false;
  }

  if (currentTime.startOf("day").isAfter(startTime)) {
    enqueueSnackbar(
      "The start date of the promotion cannot be earlier than the current date",
      {
        variant: "error",
      }
    );
    return false;
  }

  if (currentTime.startOf("day").isAfter(endTime)) {
    enqueueSnackbar(
      "The end date of the promotion cannot be earlier than the current date",
      {
        variant: "error",
      }
    );
    return false;
  }

  const { scheduleMode } = updatedValues;

  if (scheduleMode && scheduleMode + "" !== "2") {
    if (!updatedValues?.startDayTime) {
      enqueueSnackbar(t("tips.repeat_start"), {
        variant: "error",
      });
      return false;
    }

    if (!updatedValues?.endDayTime) {
      enqueueSnackbar(t("tips.endTime_empty"), {
        variant: "error",
      });
      return false;
    }
  }

  let everyDayStartTime = dayjs(updatedValues?.startDayTime, "HH:mm:ss"); // 当日 促销开始时间
  let everyDayEndTime = dayjs(updatedValues?.endDayTime, "HH:mm:ss"); // 当日 促销结束时间

  // 比较 结束促销时间是否在开始促销时间之前
  if (endTime?.isBefore(startTime)) {
    enqueueSnackbar(t("tips.promotion_not"), {
      variant: "error",
    });
    return false;
  }

  // 比较当日 促销执行时间 结束时间是否在开始时间之前
  if (everyDayEndTime?.isBefore(everyDayStartTime)) {
    enqueueSnackbar(t("tips.promotion_shall"), {
      variant: "error",
    });
    return false;
  }

  const { fullDay } = updatedValues;

  // 检验每日
  if (scheduleMode == "3") {
    if (fullDay) {
      return true;
    }
    if (endOrNot) {
      return true;
    }
    const daysDiff = endTime.diff(startTime, "day");
    if (daysDiff > 0) {
      return true;
    }
    for (
      let s = startTime;
      s.isBefore(endTime);
      s = s.add(1, "day").startOf("day")
    ) {
      const day = s.format("YYYY-MM-DD");
      const eStart = dayjs(
        day + " " + updatedValues?.startDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 0);
      const eEnd = dayjs(
        day + " " + updatedValues?.endDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 59);
      if (!(startTime.isAfter(eEnd) || endTime.isBefore(eStart))) {
        return true;
      }
    }

    enqueueSnackbar(
      "There is no overlap between promotion time and repetition time",
      {
        variant: "error",
      }
    );
    return false;
  }

  // 检验每周
  if (scheduleMode == "4") {
    const { days } = updatedValues;

    if (!days || days.length === 0) {
      enqueueSnackbar(t("tips.week_no_empty"), {
        variant: "error",
      });
      return false;
    }

    if (endOrNot) {
      return true;
    }

    for (
      let s = startTime;
      s.isBefore(endTime);
      s = s.add(1, "day").startOf("day")
    ) {
      const week = s.format("dddd");

      if (!days.includes(week)) {
        continue;
      }

      const day = s.format("YYYY-MM-DD");
      const eStart = dayjs(
        day + " " + updatedValues?.startDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 0);
      const eEnd = dayjs(
        day + " " + updatedValues?.endDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 59);
      if (!(startTime.isAfter(eEnd) || endTime.isBefore(eStart))) {
        return true;
      }
    }

    enqueueSnackbar(t("tips.overlap"), {
      variant: "error",
    });
    return false;
  }

  // 检验每月
  if (scheduleMode == "5") {
    const { months } = updatedValues;

    if (!months || months.length === 0) {
      enqueueSnackbar(t("tips.month_no"), {
        variant: "error",
      });
      return false;
    }

    let { dates } = updatedValues;
    if (!dates || dates.length === 0) {
      enqueueSnackbar(t("tips.date_no"), {
        variant: "error",
      });
      return false;
    }

    if (endOrNot) {
      return true;
    }

    const daysDiff = endTime.diff(startTime, "day");
    if (daysDiff > 366 * 8) {
      return true;
    }

    for (
      let s = startTime;
      s.isBefore(endTime);
      s = s.add(1, "day").startOf("day")
    ) {
      const month = Number(s.format("M"));

      if (!months.includes(month)) {
        continue;
      } else {
        const date = s.get("date");
        if (!dates.includes(date)) {
          continue;
        }
      }

      const day = s.format("YYYY-MM-DD");
      const eStart = dayjs(
        day + " " + updatedValues?.startDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 0);
      const eEnd = dayjs(
        day + " " + updatedValues?.endDayTime,
        "YYYY-MM-DD HH:mm:ss"
      ).set("second", 59);
      if (!(startTime.isAfter(eEnd) || endTime.isBefore(eStart))) {
        return true;
      }
    }

    enqueueSnackbar(t("tips.repetition_time"), {
      variant: "error",
    });
    return false;
  }

  // 检验每年
  if (scheduleMode == "6") {
    const { years, months } = updatedValues;

    if (!years || years.length === 0) {
      enqueueSnackbar(t("tips.year_no"), {
        variant: "error",
      });
      return false;
    }

    if (!months || months.length === 0) {
      enqueueSnackbar(t("tips.month_no"), {
        variant: "error",
      });
      return false;
    }

    let { dates } = updatedValues;
    if (!dates || dates.length === 0) {
      enqueueSnackbar(t("tips.date_no"), {
        variant: "error",
      });
      return false;
    }

    for (let i = 0; i < years.length; i++) {
      for (let j = 0; j < months.length; j++) {
        for (let k = 0; k < dates.length; k++) {
          let date = dates[k];

          let monthStr = months[j];

          if (monthStr < 10) {
            monthStr = "0" + monthStr;
          }

          const startOfMonth = dayjs(
            `${years[i]}-${monthStr}-01`,
            "YYYY-MM-DD"
          );
          const daysInMonth = startOfMonth.daysInMonth();
          if (daysInMonth < date) {
            continue;
          }

          if (date < 10) {
            date = "0" + date;
          }

          const day = `${years[i]}-${monthStr}-${date}`;
          const eStart = dayjs(
            day + " " + updatedValues?.startDayTime,
            "YYYY-MM-DD HH:mm:ss"
          ).set("second", 0);
          const eEnd = dayjs(
            day + " " + updatedValues?.endDayTime,
            "YYYY-MM-DD HH:mm:ss"
          ).set("second", 59);
          if (
            !(
              startTime.isAfter(eEnd) ||
              (!endOrNot && endTime.isBefore(eStart))
            )
          ) {
            return true;
          }
        }
      }
    }

    enqueueSnackbar(t("tips.repetition_time"), {
      variant: "error",
    });
    return false;
  }

  return true;
};
