import request from "@/util/request";
import { data } from "autoprefixer";

const baseUrl = "/nt/v1";

export const getPage = (params) => {
  return request({
    url: `${baseUrl}/screen/page`,
    method: "GET",
    params: params,
  });
};

export const getList = (params) => {
  return request({
    url: `${baseUrl}/screen`,
    method: "GET",
    params: params,
  });
};

export const edit = (id, params) => {
  return request({
    url: `${baseUrl}/screen/${id}`,
    method: "PUT",
    data: params,
  });
};

export const batchEdit = (params) => {
  return request({
    url: `${baseUrl}/screen/batch`,
    method: "PUT",
    data: params,
  });
};

export const getDetail = (id) => {
  return request({
    url: `${baseUrl}/screen/${id}`,
    method: "GET",
  });
};

export const issue = (params) => {
  return request({
    url: `/nt/v1/screen/screen_image`,
    method: "POST",
    data: params,
  });
};

export const getScreenLog = (params) => {
  return request({
    url: `${baseUrl}/screen_log/page`,
    method: "GET",
    params: params,
  });
};



export const getDownloadExcel = () => {
  return request({
    url: `${baseUrl}/screen/excel_download`,
    method: "GET",
    responseType: "blob",
  });
}


export const importScreen = (params) => {
  let formData = new FormData();
  formData.append("file", params.file);
  return request({
    url: `${baseUrl}/screen/excel_import`,
    method: "POST",
    data: formData,
    headers: { "Content-Type": "multipart/form-data" },
  });
}

export const Importprogress = (taskId) => {
  request({
    url: `${baseUrl}/screen/excel_import/progress/${taskId}`,
    method: "GET",
  });
}