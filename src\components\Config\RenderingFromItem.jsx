import { Grid } from "@mui/material";
import ZkSelect from "./ZkSelect";
import ZkInput from "./ZkInput";
import ZkSwitch from "./ZkSwitch";
import ZkInputTreeSelect from "./ZkInputTreeSelect";
import ZkTextarea from "./ZkTextarea";
import ZkRadio from "./ZkRadio";
import ZkAutocomplete from "./ZkAutocomplete";
import CustomDate from "@/pages/price/Schedule/CustomDatePicker.jsx";
import CustomTime from "@/pages/price/Schedule/TimePickerDate.jsx";
const RenderingFromItem = (props) => {
  const {
    config,
    renderingCustomItem = () => {
      return "没有自定义渲染方法";
    },
    formik = null,
    labelpostion = "top",
    defaultBox = (result, item, index) => {
      return (
        <Grid
          item
          key={item.name + index}
          sx={{ color: "red" }}
          xs={item.sx ? item.sx : props.sx}>
          {result}
        </Grid>
      );
    },
  } = props;

  let formConfig = config.map((item, index) => {
    const { type, conditionalRendering, ...orther } = item;

    orther.labelpostion = labelpostion;

    if (item.custom) {
      if (conditionalRendering) {
        if (conditionalRendering(formik)) {
          return item.renderingCustomItem
            ? item.renderingCustomItem(item, formik)
            : renderingCustomItem(item, formik);
        } else {
          return null;
        }
      } else {
        return item.renderingCustomItem
          ? item.renderingCustomItem(item, formik)
          : renderingCustomItem(item, formik);
      }
    } else {
      if (conditionalRendering) {
        //条件渲染
        if (conditionalRendering(formik)) {
          if (type === "input" || type === "password") {
            let result = (
              <ZkInput key={item.name} formik={formik} {...orther}></ZkInput>
            );
            return defaultBox(result, item);
          } else if (type === "select") {
            let result = (
              <ZkSelect key={item.name} formik={formik} {...orther}></ZkSelect>
            );
            return defaultBox(result, item);
          } else if (type === "Radio") {
            let result = (
              <ZkRadio key={item.name} formik={formik} {...orther}></ZkRadio>
            );
            return defaultBox(result, item);
          } else if (type === "selectTree") {
            let result = (
              <ZkInputTreeSelect
                key={item.name}
                formik={formik}
                treeData={item.data}
                valueKey={item.valueKey || "id"}
                labelKey={item.labelKey || "label"}
                selectd={item.selectd}
                {...orther}></ZkInputTreeSelect>
            );
            return defaultBox(result, item);
          } else if (type === "switch") {
            let result = (
              <ZkSwitch key={item.name} formik={formik} {...orther}></ZkSwitch>
            );
            return defaultBox(result, item);
          } else if (type === "textArea") {
            let result = (
              <ZkTextarea
                key={item.name}
                formik={formik}
                {...orther}></ZkTextarea>
            );
            return defaultBox(result, item);
          } else if (type === "auto") {
            let result = (
              <ZkAutocomplete
                key={item.name}
                formik={formik}
                {...orther}></ZkAutocomplete>
            );
            return defaultBox(result, item);
          } else if (type === "date") {
            let result = (
              <CustomDate
                key={item.name}
                formik={formik}
                {...orther}></CustomDate>
            );
            return defaultBox(result, item);
          } else if (type === "time") {
            let result = (
              <CustomTime
                key={item.name}
                formik={formik}
                {...orther}></CustomTime>
            );
            return defaultBox(result, item);
          } else {
            return `暂无支持${type}类型表单`;
          }
        } else {
          return null;
        }
      } else {
        if (type === "input" || type === "password") {
          let result = (
            <ZkInput
              key={item.name}
              formik={formik}
              type={type}
              {...orther}></ZkInput>
          );
          return defaultBox(result, item);
        } else if (type === "select") {
          let result = (
            <ZkSelect key={item.name} formik={formik} {...orther}></ZkSelect>
          );
          return defaultBox(result, item);
        } else if (type === "Radio") {
          let result = (
            <ZkRadio key={item.name} formik={formik} {...orther}></ZkRadio>
          );
          return defaultBox(result, item);
        } else if (type === "selectTree") {
          let result = (
            <ZkInputTreeSelect
              key={item.name}
              formik={formik}
              treeData={item.data}
              valueKey={item.valueKey || "id"}
              labelKey={item.labelKey || "label"}
              selectd={item.selectd}
              {...orther}></ZkInputTreeSelect>
          );
          return defaultBox(result, item);
        } else if (type === "switch") {
          let result = (
            <ZkSwitch key={item.name} formik={formik} {...orther}></ZkSwitch>
          );
          return defaultBox(result, item);
        } else if (type === "textArea") {
          let result = (
            <ZkTextarea
              key={item.name}
              formik={formik}
              {...orther}></ZkTextarea>
          );
          return defaultBox(result, item);
        } else if (type === "auto") {
          let result = (
            <ZkAutocomplete
              key={item.name}
              formik={formik}
              {...orther}></ZkAutocomplete>
          );
          return defaultBox(result, item);
        } else if (type === "date") {
          let result = (
            <CustomDate
              key={item.name}
              formik={formik}
              {...orther}></CustomDate>
          );
          return defaultBox(result, item);
        } else if (type === "time") {
          let result = (
            <CustomTime
              key={item.name}
              formik={formik}
              {...orther}></CustomTime>
          );
          return defaultBox(result, item);
        } else {
          return `暂无支持${type}类型表单`;
        }
      }
    }
  });
  return formConfig;
};

export default RenderingFromItem;
