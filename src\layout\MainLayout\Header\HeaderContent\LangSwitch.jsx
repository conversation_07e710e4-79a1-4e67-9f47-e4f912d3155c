import { useRef, useState } from "react";

// material-ui
import { useTheme } from "@mui/material/styles";
import {
  Avatar,
  Badge,
  Box,
  MenuItem,
  Select,
  Stack,
  ClickAwayListener,
  Divider,
  IconButton,
  List,
  ListItemButton,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Popper,
  Typography,
  useMediaQuery,
} from "@mui/material";
// lang
import { useTranslation } from "react-i18next";
// project import
import MainCard from "@/components/MainCard";
import Transitions from "@/components/@extended/Transitions";

// assets
import {
  GlobalOutlined,
  CloseOutlined,
  GiftOutlined,
  MessageOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { getStoreLang, setStoreLang } from "@/util/langUtils";
import { syncLanguage } from "@/util/microAppI18nSync";
import i18n from "@/lang/i18n";

// sx styles
const avatarSX = {
  width: 36,
  height: 36,
  fontSize: "1rem",
};

const actionSX = {
  mt: "6px",
  ml: 1,
  top: "auto",
  right: "auto",
  alignSelf: "flex-start",

  transform: "none",
};

const LangSwitch = () => {
  const { i18n } = useTranslation();
  const theme = useTheme();
  const matchesXs = useMediaQuery(theme.breakpoints.down("md"));

  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const iconBackColorOpen = "grey.300";
  const iconBackColor = "grey.100";
  const switchLang = (lang) => {
    // 使用同步方法确保在微前端环境中通知主应用
    syncLanguage(i18n, lang);
    // 刷新
    location.reload();
  };
  return (
    <Box sx={{ flexShrink: 0, ml: 0.75, pr: 2 }}>
      <IconButton
        disableRipple
        color="secondary"
        sx={{
          color: "text.primary",
          bgcolor: open ? iconBackColorOpen : iconBackColor,
        }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? "profile-grow" : undefined}
        aria-haspopup="true"
        onClick={handleToggle}>
        <GlobalOutlined />
      </IconButton>
      <Popper
        placement={matchesXs ? "bottom" : "bottom-end"}
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [matchesXs ? -5 : 0, 9],
              },
            },
          ],
        }}>
        {({ TransitionProps }) => (
          <Transitions type="fade" in={open} {...TransitionProps}>
            <Paper
              sx={{
                boxShadow: theme.customShadows.z1,
                width: "100%",
                minWidth: 128,
                maxWidth: 228,
                [theme.breakpoints.down("md")]: {
                  maxWidth: 128,
                },
              }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard border={false} content={false}>
                  <List
                    component="nav"
                    sx={{
                      p: 0,
                      "& .MuiListItemButton-root": {
                        py: 0.5,
                        "& .MuiAvatar-root": avatarSX,
                        "& .MuiListItemSecondaryAction-root": {
                          ...actionSX,
                          position: "relative",
                        },
                      },
                    }}>
                    <ListItemButton onClick={() => switchLang("zh")}>
                      <ListItemText
                        primary={
                          <Stack
                            direction="row"
                            justifyContent="flex-start"
                            alignItems="center"
                            spacing={1}>
                            {/* <img
                              loading="lazy"
                              width="20"
                              src={`https://flagcdn.com/w20/cn.png`}
                              srcSet={`https://flagcdn.com/w40/cn.png 2x`}
                              alt=""
                            /> */}
                            <Typography variant="h6">中文(简体)</Typography>
                          </Stack>
                        }
                      />
                    </ListItemButton>
                    <ListItemButton onClick={() => switchLang("en")}>
                      <ListItemText
                        primary={
                          <Stack
                            direction="row"
                            justifyContent="flex-start"
                            alignItems="center"
                            spacing={1}>
                            {/* <img
                              loading="lazy"
                              width="20"
                              src={`https://flagcdn.com/w20/us.png`}
                              srcSet={`https://flagcdn.com/w40/us.png 2x`}
                              alt=""
                            /> */}
                            <Typography variant="h6">English</Typography>{" "}
                          </Stack>
                        }
                      />
                    </ListItemButton>
                    {/* <ListItemButton onClick={() => switchLang("es")}>
                      <ListItemText
                        primary={
                          <Stack
                            direction="row"
                            justifyContent="flex-start"
                            alignItems="center"
                            spacing={1}>
                            <img
                              loading="lazy"
                              width="20"
                              src={`https://flagcdn.com/w20/es.png`}
                              srcSet={`https://flagcdn.com/w40/es.png 2x`}
                              alt=""
                            />
                            <Typography variant="h6">Español</Typography>
                          </Stack>
                        }
                      />
                    </ListItemButton> */}

                    {/* <ListItemButton onClick={() => switchLang("jp")}>
                      <ListItemText
                        primary={
                          <Stack
                            direction="row"
                            justifyContent="flex-start"
                            alignItems="center"
                            spacing={1}>
                            <img
                              loading="lazy"
                              width="20"
                              src={`https://flagcdn.com/w20/es.png`}
                              srcSet={`https://flagcdn.com/w40/es.png 2x`}
                              alt=""
                            />
                            <Typography variant="h6">日本語</Typography>
                          </Stack>
                        }
                      />
                    </ListItemButton> */}

                    {/* <ListItemButton onClick={() => switchLang("pt")}>
                      <ListItemText
                        primary={
                          <Stack
                            direction="row"
                            justifyContent="flex-start"
                            alignItems="center"
                            spacing={1}>
                            <img
                              loading="lazy"
                              width="20"
                              src={`https://flagcdn.com/w20/es.png`}
                              srcSet={`https://flagcdn.com/w40/es.png 2x`}
                              alt=""
                            />
                            <Typography variant="h6">Português</Typography>
                          </Stack>
                        }
                      />
                    </ListItemButton> */}
                  </List>
                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
};

export default LangSwitch;
