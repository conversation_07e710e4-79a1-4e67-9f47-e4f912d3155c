import CommonUtil from "@u/CommonUtils";
import IconHandaler from "@c/IconHandaler";
import DeleteIcon from "@a/images/Delete_Icon.svg?react";
import { toast } from "react-toastify";
import { confirmAlert } from "react-confirm-alert";
import ConfirmModal from "@c/ConfirmModel";
import AuthButton from "@c/AuthButton.jsx";
import { deleteGateway } from "@s/gateway.js";

export const getColums = (t, loadData) => {
  let columns = [
    {
      field: "sn",
      headerName: `${t("screen.gatewaySerialNumber")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip title={e.row.sn} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.sn)}</span>
        </Tooltip>
      ),
    },
    {
      field: "deviceAlias",
      headerName: `${t("screen.gatewayName")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip title={e.row.deviceAlias} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.deviceAlias)}</span>
        </Tooltip>
      ),
    },
    {
      field: "deviceModel",
      headerName: `${t("screen.deviceModel")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip title={e.row.deviceModel} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.deviceModel)}</span>
        </Tooltip>
      ),
    },
    {
      field: "deviceType",
      headerName: `${t("screen.deviceType")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip title={e.row.deviceType} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.deviceType)}</span>
        </Tooltip>
      ),
    },
    {
      field: "outletName",
      headerName: `${t("screen.outlet")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip title={e.row.outletName} arrow placement="bottom">
          <span>{e.row.outletName}</span>
        </Tooltip>
      ),
    },
    {
      field: "status",
      headerName: `${t("screen.gatewayRunningStatus")}`,
      flex: 1,
      headerAlign: "center", // 使列标题居中
      align: "center", // 使数据单元格内容居中
      renderCell: (e) => (
        <Tooltip
          title={e.row.status == 1 ? t("menu.online") : t("menu.offline")}
          arrow
          placement="bottom">
          <span style={{ color: e.row.status == "1" ? "green" : "red" }}>
            {CommonUtil.formatLongText(
              e.row.status == 1 ? t("menu.online") : t("menu.offline")
            )}
          </span>
        </Tooltip>
      ),
    },
    {
      headerName: `${t("common.actions")}`,
      sortable: false,
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (e) => (
        <IconHandaler>
          <AuthButton button="nt:nutag:device:delete">
            <Tooltip title={t("common.delete")} arrow sx={{ marginLeft: 1 }}>
              <DeleteIcon
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  height: "17px",
                  width: "20px",
                  padding: "2px",
                }}
                onClick={() => deleteDevice(e.id, t, loadData)}
              />
            </Tooltip>
          </AuthButton>
        </IconHandaler>
      ),
    },
  ];
  return columns;
};

const deleteDevice = (id, t, loadData) => {
  let ids = [];
  ids.push(id);
  confirmAlert({
    customUI: ({ onClose }) => {
      return (
        <ConfirmModal
          open={true}
          text={t("tips_screens.confirm_unbind_device")}
          onConfirm={() => {
            deleteGateway({
              ids: ids,
            }).then((res) => {
              toast.success(res?.message);
              loadData();
              navigate("/gateway");
            });
            onClose();
          }}
          onClose={() => onClose()}
        />
      );
    },
  });
};
