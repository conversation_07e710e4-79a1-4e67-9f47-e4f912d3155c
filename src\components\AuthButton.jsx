import PropTypes from "prop-types";

const AuthButton = (props) => {
  const { button = "", children } = props;
  // 检查用户是否具有其中任何一个权限
  const permission = JSON.parse(
    sessionStorage.getItem("USER_INFO")
  ).permissions;

  // 将逗号分隔的字符串转换为数组
  const buttonArray = button.split(",").map((item) => item.trim());
  const hasPermission = buttonArray.some(
    (btn) => permission.includes("*:*:*") || permission.includes(btn)
  );
  return hasPermission ? children : null;
};

// 校验数据类型
AuthButton.propTypes = {
  button: PropTypes.string,
};
export default AuthButton;
