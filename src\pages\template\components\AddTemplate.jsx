import React from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import CustomInput from "@c/CustomInput";
import RightViewLayout from "@c/RighViewLayout";
import { list } from "@s/resolution.js";
import { getDetail } from "@s/TemplateService.js";
import CommonUtil from "@u/CommonUtils.js";
function AddTemplate() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [image, setImage] = useState();
  const direction = [
    { id: "0", value: t("dictionary.vertical") },
    { id: "1", value: t("dictionary.horizontal") },
  ];

  let templateTypeOptions = [
    { id: "0", value: t("dictionary.generaluse") },
    { id: "1", value: t("dictionary.discount") },
    { id: "2", value: t("dictionary.byunit") },
    { id: "3", value: t("dictionary.by_value") },
    { id: "4", value: t("dictionary.promotion") },
  ];
  const [model, setModel] = useState([]);
  const [resolution, setResolution] = useState([]);
  const [modelList, setModelList] = useState([]);
  const myMap = new Map();

  const [payload, setPayload] = useState({
    name: "",
    orientation: null,
    resolution: null,
    size: "",
    model: null,
    promotionType: null,
  });
  const [error, setError] = useState({
    name: "",
    orientation: null,
    resolution: null,
    size: "",
    model: null,
    promotionType: null,
  });

  useEffect(() => {
    list().then((res) => {
      setResolution(res?.data?.valueList);
      setModelList(res?.data?.modelList);
    });

    if (state?.type == "editor") {
      getDetail(state?.id).then((res) => {
        const selectedDirection = direction.find(
          (item) => item.id == res?.data?.orientation
        );

        const selectedType = templateTypeOptions.find(
          (item) => item.id == res?.data?.promotionType
        );

        setPayload({
          ...payload,
          ...res.data,
          orientation: selectedDirection, // 默认选择第一个选项
          promotionType: selectedType,
        });
      });
    }
  }, []);

  useEffect(() => {
    for (let key in modelList) {
      if (modelList.hasOwnProperty(key)) {
        const modelArray = modelList[key].map((item) => item.model);
        myMap.set(key, modelArray);
      }
    }
  }, [myMap]);

  const validateForm = () => {
    if (!CommonUtil.isEmptyString(payload.name)) {
      if (payload.name.length > 50) {
        setError({
          ...error,
          name: t("tips_Template.characters"),
        });
        return false;
      }
    }

    if (CommonUtil.isEmptyString(payload.name)) {
      setError({
        ...error,
        name: t("tips.required"),
      });
      return false;
    }
    if (CommonUtil.isEmptyString(payload.resolution)) {
      setError({
        ...error,
        resolution: t("tips.required"),
      });
      return false;
    }
    if (CommonUtil.isEmptyString(payload.model)) {
      setError({
        ...error,
        model: t("tips.required"),
      });
      return false;
    }
    if (CommonUtil.isEmptyString(payload.promotionType)) {
      setError({
        ...error,
        type: t("tips.required"),
      });
      return false;
    }

    return true;
  };

  const handleChange = (event) => {
    const name = event.target.name;
    setPayload({
      ...payload,
      [name]: event.target.value,
    });

    setError({
      ...error,
      [name]: "",
    });
  };

  const handleSubmit = (e) => {
    if (validateForm()) {
      if (state?.type == "editor") {
        const name = payload.name;
        const resolution = payload.resolution;
        const orientation = payload.orientation?.id;
        const model = payload.model;
        const type = payload.promotionType?.id;
        const templateObjectKey = payload.templateObjectKey;
        const templateImage = image;
        const id = payload.id;
        const imageObjectKey = payload.imageDownloadObjectKey;

        const templateJson = payload.templateJson;

        navigate("/editor", {
          state: {
            name,
            resolution,
            orientation,
            model,
            type,
            templateObjectKey,
            templateImage,
            id,
            imageObjectKey,
            templateJson,
            isEditor: true,
          },
        });
      } else {
        const name = payload.name;
        const resolution = payload.resolution;
        const orientation = payload.orientation?.id;
        const model = payload.model;
        const type = payload.promotionType?.id;

        navigate("/editor", {
          state: {
            name,
            resolution,
            orientation,
            model,
            type,
            isEditor: false,
          },
        });
      }
    }
  };

  const handleSave = (e) => {
    if (validateForm()) {
      updateTemplate(payload).then((res) => {
        enqueueSnackbar(t("tips_Template.updated"), {
          variant: "success",
        });
        navigate("/editor");
      });
    }
  };

  return (
    <React.Fragment>
      <RightViewLayout
        id="addtempback"
        title={
          state?.type == "editor"
            ? t("template.edit_template")
            : t("template.add_template")
        }
        navigateBack={"/template"}
        isShowSearch={true}>
        <Card elevation={0} sx={{ height: "100%" }}>
          <Grid container p={3}>
            <Grid container xs={12} md={12} item spacing={2}>
              <Grid item md={6} xs={12}>
                <CustomInput
                  id="AddTemplate1"
                  required
                  label={t("template.name")}
                  size="small"
                  name="name"
                  value={payload.name}
                  error={error.name}
                  resetError={() => setError({ ...error, name: "" })}
                  inputProps={{
                    maxLength: 50,
                  }}
                  handleChange={handleChange}
                  helperText={error.name}
                  placeholder={t("tips_Template.name")}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.resolution")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </InputLabel>
                <Autocomplete
                  id="AddTemplateResolution"
                  options={resolution}
                  getOptionLabel={(option) => option}
                  value={payload.resolution}
                  onChange={(e, v) => {
                    setPayload({
                      ...payload,
                      resolution: v ? v : "",
                      model: null,
                    });
                    setError({
                      ...error,
                      resolution: `${t("")}`,
                    });
                    if (v !== null) {
                      setModel(myMap.get(v));
                    }
                    if (v === null) {
                      setModel([]);
                    }
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="resolution"
                      size="small"
                      error={error.resolution}
                      helperText={error.resolution}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.screen_model")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </InputLabel>

                <Autocomplete
                  id="AddTemplateModel"
                  noOptionsText={t("tips.no_options")}
                  options={model}
                  value={payload.model}
                  getOptionLabel={(option) => option}
                  onChange={(e, v) => {
                    setPayload({
                      ...payload,
                      model: v,
                    });
                    setError({
                      ...error,
                      model: `${t("")}`,
                    });
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="screenModel"
                      size="small"
                      error={error.model}
                      helperText={error.model}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.screen_direction")}
                </InputLabel>
                <Autocomplete
                  id="AddTemplateDirection"
                  options={direction}
                  getOptionLabel={(option) => option.value || ""}
                  value={payload.orientation}
                  onChange={(e, v) => {
                    setPayload({
                      ...payload,
                      orientation: v,
                    });
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="orientation"
                      size="small"
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item md={6} xs={12}>
                <InputLabel
                  shrink
                  htmlFor="bootstrap-input"
                  style={{ paddingLeft: "0px" }}>
                  {t("template.type")} <span style={{ color: "red" }}>*</span>
                </InputLabel>
                <Autocomplete
                  id="AddTemplateType"
                  options={templateTypeOptions}
                  getOptionLabel={(option) => option.value || ""}
                  value={payload.promotionType}
                  onChange={(e, v) => {
                    setPayload({
                      ...payload,
                      promotionType: v,
                    });
                    setError({
                      ...error,
                      promotionType: `${t("")}`,
                    });
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      name="type"
                      size="small"
                      error={error.promotionType}
                      helperText={error.promotionType}
                      sx={{
                        "& .MuiOutlinedInput-input.MuiInputBase-inputSizeSmall":
                          {
                            fontSize: "13px",
                            padding: "12px",
                          },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid container md={12} xs={12}>
                <Box
                  display={"flex"}
                  flexDirection={"row-reverse"}
                  style={{ marginTop: "30px", width: "100%" }}>
                  <Box item>
                    <Button
                      id="addtempnext"
                      variant="contained"
                      size="large"
                      className="text-transform-none"
                      onClick={handleSubmit}
                      style={{
                        size: "medium",
                        background:
                          "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                        borderRadius: "8px",
                        opacity: 1,
                      }}>
                      {t("common.next")}
                    </Button>
                  </Box>
                  {state?.type == "editor" && (
                    <Box item mr={2}>
                      <Button
                        id="addtempnext"
                        variant="outlined"
                        size="large"
                        className="text-transform-none"
                        onClick={handleSave}>
                        {t("common.save")}
                      </Button>
                    </Box>
                  )}

                  <Box item mr={2}>
                    <Button
                      id="addtempcan"
                      className="text-transform-none"
                      variant="outlined"
                      onClick={() => navigate(REACT_TEMPLATE_LIST)}
                      size="large">
                      {t("common.cancel")}
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Card>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddTemplate;
