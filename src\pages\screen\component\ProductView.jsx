import { Grid } from "@mui/material";
import React, { useEffect, useState } from "react";
import "@p/editor/fonts/fonts.css";
// import "./fonts/fonts.css";
import { getImage } from "@/services/TemplateService";
import { useTranslation } from "react-i18next";
const ProductView = (props) => {
  const [json, setJson] = useState(null);
  const [url, setUrl] = useState(null);
  const [urlError, SetUrlError] = useState(false);
  const [loading, setLoading] = useState(false);
  const elementId = props.id || "preViewId";
  const { layoutJSON, callback = () => {} } = props;
  const { t } = useTranslation();

  useEffect(() => {
    if (layoutJSON && !loading) {
      // 防止在 loading 状态下重复调用
      setUrl(""); // 清空之前的 URL
      let parsedJson;
      if (typeof layoutJSON === "object") {
        parsedJson = layoutJSON; // 如果已经是对象，则直接使用
      } else {
        try {
          parsedJson = JSON.parse(layoutJSON); // 尝试解析成对象
        } catch (error) {
          console.error("Error parsing layoutJSON:", error);
          parsedJson = null;
        }
      }

      // 只有在 JSON 解析成功后才进行后续操作
      if (Object.keys(parsedJson).length !== 0) {
        setJson(parsedJson);
        setLoading(true); // 设置为 loading
        SetUrlError(false);

        getImage({
          templateJson: JSON.stringify(layoutJSON),
        })
          .then((res) => {
            if (res.code == "00000000") {
              setUrl(res.data?.templateBase64);
              callback(res.data?.templateBase64);
              setLoading(false);
            } else {
              setUrl(null);
              callback("");
              SetUrlError(true);
              setLoading(false);
            }
          })
          .finally(() => {
            setLoading(false); // 请求完成后关闭 loading
          });
      }
    } else if (!layoutJSON) {
      setJson(null);
    }
  }, [layoutJSON]); // 依赖于 layoutJSON 和 loading 状态

  return (
    <Grid
      className="boundary-element"
      id={elementId}
      sx={{
        width: json?.width || 200,
        height: json?.height || 200,
        // backgroundColor: json?.bgColor||'#ffffff',
        position: "relative",
        shapeRendering: "crispEdges",
        imageRendering: "auto",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}>
      {url && (
        <img
          src={url}
          style={{
            imageRendering: "auto",
          }}></img>
      )}
      {loading && !url && <Grid>{t("product.PreView.474740-0")}</Grid>}
      {urlError && <Grid>{t("product.PreView.474740-1")}</Grid>}
    </Grid>
  );
};
export default ProductView;
