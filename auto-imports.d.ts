/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const Accordion: typeof import('@mui/material')['Accordion']
  const AccordionActions: typeof import('@mui/material')['AccordionActions']
  const AccordionDetails: typeof import('@mui/material')['AccordionDetails']
  const AccordionSummary: typeof import('@mui/material')['AccordionSummary']
  const Alert: typeof import('@mui/material')['Alert']
  const AlertTitle: typeof import('@mui/material')['AlertTitle']
  const AppBar: typeof import('@mui/material')['AppBar']
  const Autocomplete: typeof import('@mui/material')['Autocomplete']
  const Avatar: typeof import('@mui/material')['Avatar']
  const AvatarGroup: typeof import('@mui/material')['AvatarGroup']
  const Backdrop: typeof import('@mui/material')['Backdrop']
  const Badge: typeof import('@mui/material')['Badge']
  const BottomNavigation: typeof import('@mui/material')['BottomNavigation']
  const BottomNavigationAction: typeof import('@mui/material')['BottomNavigationAction']
  const Box: typeof import('@mui/material')['Box']
  const Breadcrumbs: typeof import('@mui/material')['Breadcrumbs']
  const Button: typeof import('@mui/material')['Button']
  const ButtonBase: typeof import('@mui/material')['ButtonBase']
  const ButtonGroup: typeof import('@mui/material')['ButtonGroup']
  const Card: typeof import('@mui/material')['Card']
  const CardActionArea: typeof import('@mui/material')['CardActionArea']
  const CardActions: typeof import('@mui/material')['CardActions']
  const CardContent: typeof import('@mui/material')['CardContent']
  const CardHeader: typeof import('@mui/material')['CardHeader']
  const CardMedia: typeof import('@mui/material')['CardMedia']
  const Checkbox: typeof import('@mui/material')['Checkbox']
  const Chip: typeof import('@mui/material')['Chip']
  const CircularProgress: typeof import('@mui/material')['CircularProgress']
  const ClickAwayListener: typeof import('@mui/material')['ClickAwayListener']
  const Collapse: typeof import('@mui/material')['Collapse']
  const Container: typeof import('@mui/material')['Container']
  const CssBaseline: typeof import('@mui/material')['CssBaseline']
  const Dialog: typeof import('@mui/material')['Dialog']
  const DialogActions: typeof import('@mui/material')['DialogActions']
  const DialogContent: typeof import('@mui/material')['DialogContent']
  const DialogContentText: typeof import('@mui/material')['DialogContentText']
  const DialogTitle: typeof import('@mui/material')['DialogTitle']
  const Divider: typeof import('@mui/material')['Divider']
  const Drawer: typeof import('@mui/material')['Drawer']
  const Fab: typeof import('@mui/material')['Fab']
  const Fade: typeof import('@mui/material')['Fade']
  const FilledInput: typeof import('@mui/material')['FilledInput']
  const FormControl: typeof import('@mui/material')['FormControl']
  const FormControlLabel: typeof import('@mui/material')['FormControlLabel']
  const FormGroup: typeof import('@mui/material')['FormGroup']
  const FormHelperText: typeof import('@mui/material')['FormHelperText']
  const FormLabel: typeof import('@mui/material')['FormLabel']
  const GlobalStyles: typeof import('@mui/material')['GlobalStyles']
  const Grid: typeof import('@mui/material')['Grid']
  const Grow: typeof import('@mui/material')['Grow']
  const Hidden: typeof import('@mui/material')['Hidden']
  const Icon: typeof import('@mui/material')['Icon']
  const IconButton: typeof import('@mui/material')['IconButton']
  const ImageList: typeof import('@mui/material')['ImageList']
  const ImageListItem: typeof import('@mui/material')['ImageListItem']
  const ImageListItemBar: typeof import('@mui/material')['ImageListItemBar']
  const Input: typeof import('@mui/material')['Input']
  const InputAdornment: typeof import('@mui/material')['InputAdornment']
  const InputBase: typeof import('@mui/material')['InputBase']
  const InputLabel: typeof import('@mui/material')['InputLabel']
  const LinearProgress: typeof import('@mui/material')['LinearProgress']
  const Link: typeof import('react-router-dom')['Link']
  const List: typeof import('@mui/material')['List']
  const ListItem: typeof import('@mui/material')['ListItem']
  const ListItemAvatar: typeof import('@mui/material')['ListItemAvatar']
  const ListItemButton: typeof import('@mui/material')['ListItemButton']
  const ListItemIcon: typeof import('@mui/material')['ListItemIcon']
  const ListItemSecondaryAction: typeof import('@mui/material')['ListItemSecondaryAction']
  const ListItemText: typeof import('@mui/material')['ListItemText']
  const ListSubheader: typeof import('@mui/material')['ListSubheader']
  const Menu: typeof import('@mui/material')['Menu']
  const MenuItem: typeof import('@mui/material')['MenuItem']
  const MenuList: typeof import('@mui/material')['MenuList']
  const MobileStepper: typeof import('@mui/material')['MobileStepper']
  const Modal: typeof import('@mui/material')['Modal']
  const NativeSelect: typeof import('@mui/material')['NativeSelect']
  const NavLink: typeof import('react-router-dom')['NavLink']
  const Navigate: typeof import('react-router-dom')['Navigate']
  const NoSsr: typeof import('@mui/material')['NoSsr']
  const Outlet: typeof import('react-router-dom')['Outlet']
  const OutlinedInput: typeof import('@mui/material')['OutlinedInput']
  const Pagination: typeof import('@mui/material')['Pagination']
  const PaginationItem: typeof import('@mui/material')['PaginationItem']
  const Paper: typeof import('@mui/material')['Paper']
  const Popover: typeof import('@mui/material')['Popover']
  const Popper: typeof import('@mui/material')['Popper']
  const Portal: typeof import('@mui/material')['Portal']
  const Radio: typeof import('@mui/material')['Radio']
  const RadioGroup: typeof import('@mui/material')['RadioGroup']
  const Rating: typeof import('@mui/material')['Rating']
  const Route: typeof import('react-router-dom')['Route']
  const Routes: typeof import('react-router-dom')['Routes']
  const ScopedCssBaseline: typeof import('@mui/material')['ScopedCssBaseline']
  const Select: typeof import('@mui/material')['Select']
  const Skeleton: typeof import('@mui/material')['Skeleton']
  const Slide: typeof import('@mui/material')['Slide']
  const Slider: typeof import('@mui/material')['Slider']
  const Snackbar: typeof import('@mui/material')['Snackbar']
  const SnackbarContent: typeof import('@mui/material')['SnackbarContent']
  const SpeedDial: typeof import('@mui/material')['SpeedDial']
  const SpeedDialAction: typeof import('@mui/material')['SpeedDialAction']
  const SpeedDialIcon: typeof import('@mui/material')['SpeedDialIcon']
  const Stack: typeof import('@mui/material')['Stack']
  const Step: typeof import('@mui/material')['Step']
  const StepButton: typeof import('@mui/material')['StepButton']
  const StepConnector: typeof import('@mui/material')['StepConnector']
  const StepContent: typeof import('@mui/material')['StepContent']
  const StepIcon: typeof import('@mui/material')['StepIcon']
  const StepLabel: typeof import('@mui/material')['StepLabel']
  const Stepper: typeof import('@mui/material')['Stepper']
  const SvgIcon: typeof import('@mui/material')['SvgIcon']
  const SwipeableDrawer: typeof import('@mui/material')['SwipeableDrawer']
  const Switch: typeof import('@mui/material')['Switch']
  const Tab: typeof import('@mui/material')['Tab']
  const TabScrollButton: typeof import('@mui/material')['TabScrollButton']
  const Table: typeof import('@mui/material')['Table']
  const TableBody: typeof import('@mui/material')['TableBody']
  const TableCell: typeof import('@mui/material')['TableCell']
  const TableContainer: typeof import('@mui/material')['TableContainer']
  const TableFooter: typeof import('@mui/material')['TableFooter']
  const TableHead: typeof import('@mui/material')['TableHead']
  const TablePagination: typeof import('@mui/material')['TablePagination']
  const TableRow: typeof import('@mui/material')['TableRow']
  const TableSortLabel: typeof import('@mui/material')['TableSortLabel']
  const Tabs: typeof import('@mui/material')['Tabs']
  const TextField: typeof import('@mui/material')['TextField']
  const TextareaAutosize: typeof import('@mui/material')['TextareaAutosize']
  const ToggleButton: typeof import('@mui/material')['ToggleButton']
  const ToggleButtonGroup: typeof import('@mui/material')['ToggleButtonGroup']
  const Toolbar: typeof import('@mui/material')['Toolbar']
  const Tooltip: typeof import('@mui/material')['Tooltip']
  const Typography: typeof import('@mui/material')['Typography']
  const Unstable_Grid2: typeof import('@mui/material')['Unstable_Grid2']
  const Zoom: typeof import('@mui/material')['Zoom']
  const createRef: typeof import('react')['createRef']
  const darkScrollbar: typeof import('@mui/material')['darkScrollbar']
  const dayjs: typeof import('dayjs')['default']
  const forwardRef: typeof import('react')['forwardRef']
  const generateUtilityClass: typeof import('@mui/material')['generateUtilityClass']
  const generateUtilityClasses: typeof import('@mui/material')['generateUtilityClasses']
  const lazy: typeof import('react')['lazy']
  const memo: typeof import('react')['memo']
  const startTransition: typeof import('react')['startTransition']
  const useAutocomplete: typeof import('@mui/material')['useAutocomplete']
  const useCallback: typeof import('react')['useCallback']
  const useContext: typeof import('react')['useContext']
  const useDebugValue: typeof import('react')['useDebugValue']
  const useDeferredValue: typeof import('react')['useDeferredValue']
  const useEffect: typeof import('react')['useEffect']
  const useHref: typeof import('react-router-dom')['useHref']
  const useId: typeof import('react')['useId']
  const useImperativeHandle: typeof import('react')['useImperativeHandle']
  const useInRouterContext: typeof import('react-router-dom')['useInRouterContext']
  const useInsertionEffect: typeof import('react')['useInsertionEffect']
  const useLayoutEffect: typeof import('react')['useLayoutEffect']
  const useLinkClickHandler: typeof import('react-router-dom')['useLinkClickHandler']
  const useLocation: typeof import('react-router-dom')['useLocation']
  const useMediaQuery: typeof import('@mui/material')['useMediaQuery']
  const useMemo: typeof import('react')['useMemo']
  const useNavigate: typeof import('react-router-dom')['useNavigate']
  const useNavigationType: typeof import('react-router-dom')['useNavigationType']
  const useOutlet: typeof import('react-router-dom')['useOutlet']
  const useOutletContext: typeof import('react-router-dom')['useOutletContext']
  const useParams: typeof import('react-router-dom')['useParams']
  const useReducer: typeof import('react')['useReducer']
  const useRef: typeof import('react')['useRef']
  const useResolvedPath: typeof import('react-router-dom')['useResolvedPath']
  const useRoutes: typeof import('react-router-dom')['useRoutes']
  const useScrollTrigger: typeof import('@mui/material')['useScrollTrigger']
  const useSearchParams: typeof import('react-router-dom')['useSearchParams']
  const useState: typeof import('react')['useState']
  const useSyncExternalStore: typeof import('react')['useSyncExternalStore']
  const useTransition: typeof import('react')['useTransition']
  const useTranslation: typeof import('react-i18next')['useTranslation']
}
