.card {
    color: #7f8c8d;
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    box-shadow: none;
    border-radius: 12px;
    // background: #d3eddb;
    margin-bottom: 22px;
    overflow: hidden;
    position: relative;
}
// .card::after {
//     content: '';
//     position: absolute;
//     width: 157px;
//     height: 157px;
//     background: #7bed9f;
//     border-radius: 50%;
//     top: -105px;
//     right: -96px;
// }
.memoryBox {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 9999;
}
