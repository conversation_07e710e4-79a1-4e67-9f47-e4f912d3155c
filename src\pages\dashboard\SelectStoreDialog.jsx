import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Button,
  Tooltip,
  Grid,
  Radio,
  Dialog,
  DialogContent,
  DialogActions,
  InputAdornment,
  IconButton,
  TextField,
  DialogContentText,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import DataTable from "@/components/DataTable";
import { useTranslation } from "react-i18next";
import CommonUtil from "@/util/CommonUtils";
import { useSnackbar } from "notistack";
import { getOutletPageList } from "@/services/common.js";
import ClearIcon from "@mui/icons-material/Clear";
import { timeZoneList } from "@/constants/TimeZone";
const SelectStoreDialog = forwardRef((props, ref) => {
  const { open, setOpen, setOutletID, setSelectStore } = props;

  const { t } = useTranslation();
  const [records, setRecords] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const [isLoading, setIsLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  //模糊搜索的值
  const [inputValue, setInputValue] = useState("");

  const defaultFilters = {
    page: 1,
    pageSize: 5,
    name: "",
  };

  const [filters, setFilters] = useState({
    page: 1,
    pageSize: 5,
    name: inputValue,
  });

  /**
   * @method getloadData
   * 加载表格数据
   */

  // 使用 useImperativeHandle 将方法暴露给父组件
  useImperativeHandle(ref, () => ({
    getloadData: getloadData,
  }));

  const getloadData = () => {
    setIsLoading(true);
    try {
      const { page, pageSize } = filters;
      getOutletPageList({ page, pageSize }).then((response) => {
        const list = response?.data?.data || [];
        list.unshift({ name: t("dashboard.all_stores"), id: "All" });
        setRecords(list);
        setTotalRecords(response?.data?.total);
        setIsLoading(false);
      });
    } catch (error) {
      enqueueSnackbar("Fail loading..." + error, {
        variant: "error",
      });
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!open) {
      return;
    }
    getloadData();
  }, [filters, open]);

  /**
   * @method handleChange
   * 根据id 保存选中行id
   */
  const [selectedValue, setSelectedValue] = React.useState("");
  const handleChange = (event, rowId) => {
    setSelectedValue(rowId);
  };

  /**
   * @method renderRadioColumn
   * 单选框 渲染方法
   */
  const renderRadioColumn = (params) => {
    const rowId = getRowId(params.row);

    return (
      <Radio
        checked={selectedValue === rowId}
        onChange={(event) => handleChange(event, rowId)}
        value={rowId}
        name="radio-buttons"
        inputProps={{ "aria-label": rowId }}
      />
    );
  };

  const getRowId = (data) => data.id;
  const columns = [
    {
      field: "radio",
      headerName: "",
      width: 50,
      sortable: false,
      renderCell: renderRadioColumn,
    },
    {
      field: "name",
      headerName: t("outlet.name"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip title={e.row.name} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.name)}</span>
        </Tooltip>
      ),
    },
    {
      field: "code",
      headerName: t("outlet.code"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip title={e.row.code} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.code)}</span>
        </Tooltip>
      ),
    },
    {
      field: "address",
      headerName: t("table.address"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip title={e.row.address} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.address)}</span>
        </Tooltip>
      ),
    },
    {
      field: "email",
      headerName: t("table.email"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip title={e.row.email} arrow placement="bottom">
          <span>{CommonUtil.formatLongText(e.row.email)}</span>
        </Tooltip>
      ),
    },
    {
      field: "timeZone",
      headerName: t("table.time_zone"),
      flex: 1,
      renderCell: (e) => (
        <Tooltip
          title={CommonUtil.getTimeZoneNameByValue(
            timeZoneList,
            e.row.timezone
          )}
          arrow
          placement="bottom">
          <span>
            {CommonUtil.formatLongText(
              CommonUtil.getTimeZoneNameByValue(e.row.timezone)
            )}
          </span>
        </Tooltip>
      ),
    },
  ];
  /**
   * @method handlePageChange
   * 切换当前页
   */
  const handlePageChange = (e) => {
    setFilters({
      ...filters,
      page: e + 1,
    });
  };

  /**
   * @method handlePageSize
   * 切换每页数量
   */
  const handlePageSize = (e) => {
    setFilters({
      page: defaultFilters.page,
      pageSize: e,
    });
  };

  /**
   * @method 关闭弹窗
   */
  const handlerCancel = () => {
    setOpen(false);
    setInputValue("");
  };
  // PCS119
  const handleSearch = () => {
    setIsLoading(true);
    if (inputValue === "") {
      enqueueSnackbar(t("PCS119"), {
        variant: "info",
      });
      getloadData();
      return;
    }
    try {
      setIsLoading(true);
      let params = {
        pageSize: filters.pageSize,
        page: 1,
        name: inputValue,
      };
      getOutletPageList(params).then((response) => {
        if (response?.code == "00000000") {
          setRecords(response?.data?.data);
          setTotalRecords(response?.data?.data?.total);
          setIsLoading(false);
        } else {
          setRecords([]);
          setTotalRecords(0);
          setIsLoading(false);
        }
      });
    } catch (error) {
      enqueueSnackbar("Fail loading..." + error, {
        variant: "error",
      });
      setIsLoading(false);
    }
  };

  /**
   * @method handleAgreeButtonClick
   * 点击确定插入数据
   */
  const handleAgreeButtonClick = () => {
    if (selectedValue) {
      let filterResult = records.find((item) => item?.id === selectedValue);
      setOutletID(filterResult?.id);
      setSelectStore(filterResult?.name);
    }

    setOpen(false);
    setInputValue("");
  };

  const handlerClear = () => {
    setInputValue("");
    setFilters({
      ...defaultFilters,
      name: "",
    });
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      handleSearch(); // 调用搜索函数
    }
  };

  return (
    <Dialog open={open} maxWidth="lg">
      <DialogContent
        sx={{
          width: "1000px",
        }}>
        <DialogContentText id="alert-dialog-description">
          <Grid container>
            <TextField
              size="small"
              label={t("tips_outlet.outlet_name")}
              variant="outlined"
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value);
              }}
              onKeyDown={handleKeyDown} // 添加 onKeyDown 事件
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      id="clearButton"
                      onClick={handlerClear}
                      style={{
                        visibility: inputValue ? "visible" : "hidden",
                      }}>
                      <ClearIcon />
                    </IconButton>
                    <IconButton onClick={handleSearch}>
                      <SearchIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          <div
            style={{
              marginTop: "10px",
              height: "500px",
            }}>
            <DataTable
              height="auto"
              rows={records || []}
              columns={columns}
              rowCount={totalRecords}
              getRowId={getRowId}
              pageSizeOptions={[5, 10, 20, 30, 50]}
              page={filters.page - 1}
              disableColumnMenu
              totalRecords={totalRecords}
              rowsPerPage={filters.pageSize}
              onPageChange={(pn) => handlePageChange(pn)}
              onPageSizeChange={(ps) => handlePageSize(ps)}
              checkboxSelection={false}
            />
          </div>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handlerCancel}>{t("common.cancel")}</Button>
        <Button onClick={handleAgreeButtonClick} autoFocus variant="contained">
          {t("common.confirm")}
        </Button>
      </DialogActions>
    </Dialog>
  );
});

export default SelectStoreDialog;
