import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  Typography,
} from "@mui/material";
import { GridExpandMoreIcon } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";
import PreView from "../../editor/PreView";
//todo
import Tick from "@a/images/Tick.svg?react";
import { findTemplateByTypeAndResolution } from "../../../services/TemplateService";
import CommonUtil from "../../../util/CommonUtils";

export default function TemplateSelector(props) {
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const [templates, setTemplates] = useState([]);

  const {
    payload,
    setPayload,
    product,
    promotionType,
    setSelectedTemplates,
    selectedTemplates,
    selectedTemplate,
    setSelectedTemplate,
    setScreenResolution,
    setElementJSON,
    settempJSON,
    resolutions,
    setDownloadUrls,
    isTemplateExpanded,
    handleToggleTemplateAccordion,
    handleSyncImmediately,
    handleToggleScheduleAccordion,
    handleToggleTemplatePrevious,
  } = props;

  const loadDataTemplates = (templateFilter) => {
    findTemplateByTypeAndResolution(templateFilter).then((res) => {
      if (res?.data?.code === "LVLI0000" && res?.data?.data) {
        setTemplates(res.data.data.templateData);

        setDownloadUrls(res?.data?.data?.downloadUrlData);
      } else if (res?.data?.code === "LVLE0054") {
        setTemplates([]);
      }
    });
  };

  useEffect(() => {
    if (resolutions.length > 0) {
      if (!CommonUtil.isEmpty(promotionType)) {
        loadDataTemplates({
          screenResolution: resolutions,
          type: promotionType,
        });
      }
    }
  }, [resolutions, promotionType]);

  const handleTemplateSelection = (template) => {
    if (selectedTemplate && selectedTemplate !== template.id) {
      setSelectedTemplates([template.id]);
      setSelectedTemplate(template.id);
    } else {
      if (selectedTemplates.includes(template.id)) {
        setSelectedTemplates(
          selectedTemplates.filter((t) => t !== template.id)
        );
        setSelectedTemplate(null);
      } else {
        setSelectedTemplates([template.id]);
        setSelectedTemplate(template.id);
      }
    }

    setElementJSON(template.templateJson);
    settempJSON(template.templateJson);
    setScreenResolution(template.screenResolution);
    setPayload({
      ...payload,
      resolution: template.screenResolution,
      templateName: template.name,
    });
  };

  const handleTemplateCancel = () => {
    setSelectedTemplates([]);
  };

  return (
    <Accordion
      elevation={0}
      expanded={isTemplateExpanded}
      onChange={handleToggleTemplateAccordion}>
      <AccordionSummary
        expandIcon={<GridExpandMoreIcon />}
        aria-controls="panel3-content"
        id="panel3-header"
        sx={{ fontFamily: "Roboto" }}>
        {t("PRCE0019")}
      </AccordionSummary>
      <AccordionDetails>
        <Typography>{t("events.selectTemplate")}</Typography>

        {product && promotionType && (
          <Grid
            sx={{
              overflowX: "auto",
              whiteSpace: "nowrap",
              display: "flex",
            }}
            spacing={1}
            pt={1}>
            {templates?.map((template) => (
              <Grid item key={template.id} style={{ margin: "10px" }}>
                <Typography>{template?.screenResolution}</Typography>

                <Grid
                  sx={{
                    flexDirection: "row",
                    display: "flex",
                    cursor: "pointer",
                  }}>
                  <div
                    onClick={() => handleTemplateSelection(template)}
                    style={{
                      border:
                        selectedTemplates &&
                        selectedTemplates.includes(template.id) &&
                        selectedTemplate
                          ? "3px solid #1487CA"
                          : "2px solid #A2A3A3",

                      width: template?.templateJson?.width,
                      height: template?.templateJson?.height,
                      position: "relative",
                    }}>
                    {selectedTemplates &&
                    selectedTemplates.includes(template.id) &&
                    selectedTemplate ? (
                      <Tick
                        style={{
                          position: "absolute",
                          top: 5,
                          right: 5,
                        }}
                      />
                    ) : null}
                    <PreView layoutJSON={template?.templateJson}></PreView>
                  </div>
                </Grid>
              </Grid>
            ))}
          </Grid>
        )}
      </AccordionDetails>
      <Grid container spacing={2} pb={2} pr={2}>
        <Grid item md={3}>
          <Box item ml={2}>
            <Button
              variant="contained"
              size="large"
              className="text-transform-none"
              style={{
                size: "medium",
                borderRadius: "8px",
                opacity: 1,
                background:
                  "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
              }}
              onClick={handleSyncImmediately}>
              {t("Sync Immediately")}
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12} md={9}>
          <Box display={"flex"} flexDirection={"row-reverse"}>
            <Box item pl={2}>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="contained"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  background:
                    "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%) 0% 0% no-repeat padding-box",
                }}
                onClick={handleToggleScheduleAccordion}>
                {t("common.next")}
              </Button>
            </Box>
            <Box item pl={2}>
              <Button
                id="AddAuthorizationLevel-button-02"
                className="text-transform-none"
                variant="outlined"
                onClick={handleToggleTemplatePrevious}
                size="large">
                {t("Previous")}
              </Button>
            </Box>
            <Box item>
              <Button
                id="AddAuthorizationLevel-button-01"
                variant="none"
                size="large"
                className="text-transform-none"
                style={{
                  size: "medium",
                  borderRadius: "8px",
                  opacity: 1,
                  color: "#1487CA",
                }}
                onClick={handleTemplateCancel}>
                {t("common.preview")}
              </Button>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Accordion>
  );
}
