import React from 'react'
// project import
import NavCard from "./NavCard";
import Navigation from "./Navigation";
import SimpleBar from "@/components/third-party/SimpleBar";
import Box from "@mui/material/Box";
// ==============================|| DRAWER CONTENT ||============================== //

const DrawerContent = () => (
  <SimpleBar
    sx={{
      "& .simplebar-content": {
        display: "flex",
        flexDirection: "column",
        background: "white",
        // border: `1px solid #f4f4f4`,

        // border: "10px solid",
        // borderImage: "linear-gradient(to bottom, #3498db, #2ecc71) 1",
        // borderRadius: "10px",
      },
      // padding: "0 3px 0 3px",
      // background: "linear-gradient(to bottom, #3498db, #2ecc71)",

      // borderStyle: "solid",
      // borderWidth: "2px",
      // borderImage: "linear-gradient(to bottom, #3498db, #2ecc71) 1",
      // borderImageSlice: 10,
    }}
  >
    <Navigation />
  </SimpleBar>
);

export default DrawerContent;
