import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import i18n from "./lang/i18n";
import ThemeCustomization from "./themes";
import { I18nextProvider } from "react-i18next";
import { Provider as ReduxProvider } from "react-redux";
import { BrowserRouter as Router } from "react-router-dom";
import { store } from "@/store";
import { setToken } from "@/util/auth";
import {
  renderWithQiankun,
  qiankunWindow,
} from "vite-plugin-qiankun/dist/helper";
import "virtual:svg-icons-register";
import { initSubAppI18n, cleanupI18nSync } from "@/util/microAppI18nSync";

const initQianKun = () => {
  renderWithQiankun({
    // 当前应用在主应用中的生命周期
    // 文档 https://qiankun.umijs.org/zh/guide/getting-started#
    mount(props) {
      const i18nConfig = initSubAppI18n(i18n, props);
      //  可以通过props读取主应用的参数：msg
      // 监听主应用传值
      render(props.container);
      if (props) {
        //把 qiankun 的props 挂载到全局变量
        window.qiankunProps = props;
      }
      props.onGlobalStateChange((res) => {
        if (res.token) {
          setToken(res.token);
          sessionStorage.setItem("USER_INFO", JSON.stringify(res?.userInfo));
        }
        if (res.menuId) {
          store.commit("SET_MENU_ID", res?.menuId);
        }
      }, true);
    },
    bootstrap() {},
    unmount() {
      cleanupI18nSync(props);
    },
  });
};

const render = (container, props = null) => {
  // 如果是在主应用的环境下就挂载主应用的节点，否则挂载到本地
  const appDom = container ? container : document.getElementById("root");
  ReactDOM.createRoot(appDom).render(
    <ReduxProvider store={store}>
      <ThemeCustomization>
        <I18nextProvider i18n={i18n}>
          <Router basename="/e-price-tag-app">
            <App props={props} />
          </Router>
        </I18nextProvider>
      </ThemeCustomization>
    </ReduxProvider>
  );
};

// 判断当前应用是否在主应用中
qiankunWindow.__POWERED_BY_QIANKUN__ ? initQianKun() : render();
