/* eslint-disable react-hooks/rules-of-hooks */
/**
 * @param {string} pathname 当前路由路径
 * @param {object} meta 当前路由自定义meta字段
 * @return {string} 需要跳转到其他页时，就返回一个该页的path路径，或返回resolve该路径的promise对象
 */
import { getToken } from "@/util/auth";
import { useDispatch } from "react-redux";
import { useDispatchMenu } from "@/store/hooks/menu";
import { getUserMenus } from "@/services/common.js";
import { useMenuInfo } from "@/store/hooks/menu.js";
import { toast } from "react-toastify";
const flattenTree = (nodes) => {
  let result = [];
  nodes.forEach((node) => {
    result.push(node.id);
    if (node.children) {
      let ids = flattenTree(node.children);
      result = [...result, ...ids];
    }
  });
  return result;
};

const onRouteBefore = async ({ pathname, meta }) => {

  if (!getToken()) {
    toast.error("登录已过期");
    setTimeout(() => {
      window.location.href = "/login";
    }, 1000);

  }
  // const { stateSetMenu } = useDispatchMenu();

  // const dispatch = useDispatch();

  // let menus = null;
  // menus = await getUserMenus({ applicationCode: "NT" }).then(async (res) => {
  //   let menus = res.data;

  //   if (menus.length == 0) {
  //     toast.error("您没有任何权限，请联系管理员");
  //     setTimeout(() => {
  //       window.location.href = "/application/center";
  //     }, 1000);


  //   } else {

  //     dispatch(stateSetMenu(menus));
  //   }
  // });
};
export default onRouteBefore;
