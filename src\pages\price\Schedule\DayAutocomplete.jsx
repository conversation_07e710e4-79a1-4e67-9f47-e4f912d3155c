import { Autocomplete, Grid, InputLabel, FormHelperText } from "@mui/material";
import CustomInput from "@c/CustomInput";
import RequirePoint from "@c/RequirePoint";
import { useEffect, useState } from "react";
import { Stack } from "@mui/system";

function ZKAutocomplete(props) {
  const {
    formik = null,
    placeholder = "",
    handleBlur,
    handleChange,
    label,
    name,
    error,
    disabled = false,
    isClear = true,
    labelpostion,
    spacing = 1,
    width,
    height = "40px",
    inputType,
    options,
    fontSize = "22px",
    readonly,
    defaultValue,
    typeValue = "0",
    noOptionsText,
    ...orther
  } = props;

  const [data, setData] = useState(null);

  useEffect(() => {
    const list = options?.find((item) => {
      return item?.id == formik.values[name];
    });

    setData(list);
    formik.setFieldValue(name, formik.values[name]);
  }, [options, formik.values]);

  return (
    <Stack>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={spacing}></Stack>
      <InputLabel
        shrink
        htmlFor={"CmpAutoComPlete_" + name}
        style={{
          marginTop: labelpostion === "left" ? "12px" : "",
          width: width,
          fontSize: fontSize,
        }}>
        {props.required && <RequirePoint></RequirePoint>}
        {label}
      </InputLabel>

      <Stack>
        <Autocomplete
          noOptionsText={noOptionsText}
          options={options}
          value={data || ""}
          name={name}
          disabled={disabled}
          onChange={(event, newValue) => {
            formik.setFieldValue(name, newValue?.id); // 保存选中的 id 到 formik
            setData(newValue); // 设置 data 为选中的对象
          }}
          getOptionLabel={(option) => {
            return option ? option?.value : "";
          }}
          renderInput={(params) => (
            <CustomInput
              fullWidth
              handleChange={(e) => {}}
              resetError={() => console.log("6666")}
              size={props.size ? props.size : " "}
              style={{ width: "100%" }}
              {...params}
              inputProps={{
                ...params.inputProps,
                placeholder: props.placeholder,
              }}
            />
          )}
          {...orther}
        />
        {((formik?.touched[name] && formik?.errors[name]) || error) && (
          <FormHelperText
            error
            id={`standard-weight-helper-text-${name}`}
            sx={{
              mt: 2,
            }}>
            {formik?.errors[name] || error}
          </FormHelperText>
        )}
      </Stack>
    </Stack>
  );
}

export default ZKAutocomplete;
