import { useEffect, useState } from "react";
import { Box, Grid, FormControlLabel, Checkbox } from "@mui/material";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import AllDay from "./AllDay";
import EveryDay from "./EveryDay";
import {
  allMonth,
  getFutureYears,
  getFormattedDaysArray,
  handlerAllCheckYears,
  handlerAllCheckMonth,
  handlerAllCheckDay,
  handleYearChange,
  handlerMonth,
  handleDayChange,
} from "../js/way";
import { getPriceChangeEventById } from "../../../services/PriceChangeEventservice";
function SetRepeatCondition(props) {
  const {
    scheduleMode,
    fullDay,
    setFullDay,
    morningTime,
    setMorningTime,
    afterTime,
    StartPromotion,
    setAfterTime,
    selectedYears,
    setSelectedYears,
    selectedMonth,
    setSelectedMonth,
    selectedDays,
    setSelectedDays,
    startDayDate,
    endDayDate,
    selectedDates,
    setSelectedDates,
  } = props;

  // console.log("DDDDDDDDDDDDDDDD", scheduleMode);
  const monthMapping = {
    January: 0,
    February: 1,
    March: 2,
    April: 3,
    May: 4,
    June: 5,
    July: 6,
    August: 7,
    September: 8,
    October: 9,
    November: 10,
    December: 11,
  };
  const { t } = useTranslation();
  const [allday, setAllDay] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isCheckedMonth, setIsCheckedMonth] = useState(false);
  const [isCheckedDay, setIsCheckedDay] = useState(false);

  useEffect(() => {
    getPriceChangeEventById(JSON.parse(localStorage.getItem("event"))?.id).then(
      (res) => {
        let data = res?.data?.data;
        setSelectedYears(data?.years);
        setSelectedMonth(data?.months);
        setSelectedDates(data?.dates);
        setMorningTime(
          dayjs(data?.startDayTime, "HH:mm:ss").format("hh:mm:ss A")
        );
        setAfterTime(dayjs(data?.endDayTime, "HH:mm:ss").format("hh:mm:ss A"));
      }
    );
  }, []);

  const hanlderAllDay = () => {
    let newAllDayState = true;
    setFullDay((previous) => {
      newAllDayState = !previous;
      if (newAllDayState) {
        setMorningTime(dayjs().startOf("day"));
        setAfterTime(dayjs().endOf("day"));
      } else {
        setMorningTime(dayjs());
        setAfterTime(dayjs());
      }
      return newAllDayState;
    });
  };

  // useEffect(() => {
  //   setAllDay(true);
  // }, [scheduleMode]);

  const years = getFutureYears(startDayDate, endDayDate);
  // 获取特定月份的所有天数
  const formattedDaysArray = getFormattedDaysArray(
    selectedMonth,
    selectedYears
  );

  useEffect(() => {
    if (selectedYears?.length < years?.length) {
      setIsChecked(false);
    } else {
      setIsChecked(true);
    }
  }, [selectedYears]);

  useEffect(() => {
    const isCurrentYear =
      dayjs(startDayDate).year() === dayjs(endDayDate).year();
    const monthsToCheck =
      dayjs(endDayDate).month() - dayjs(startDayDate).month() + 1;

    if (isCurrentYear && selectedMonth?.length === monthsToCheck) {
      setIsCheckedMonth(true);
    }
    if (isCurrentYear && selectedMonth?.length !== monthsToCheck) {
      setIsCheckedMonth(false);
    }
    if (!isCurrentYear && selectedMonth?.length < 12) {
      setIsCheckedMonth(false);
    }
    if (selectedMonth?.length === 12) {
      setIsCheckedMonth(true);
    }
    if (selectedMonth?.length === 0) {
      setIsCheckedMonth(false);
    }
  }, [startDayDate, endDayDate, selectedMonth]);

  useEffect(() => {
    const isCurrentYear =
      dayjs(startDayDate).year() === dayjs(endDayDate).year();
    const isCurrentMonth =
      selectedMonth?.length === 1 && dayjs(startDayDate).month();
    const datesToCheck =
      dayjs(endDayDate).date() - dayjs(startDayDate).date() + 1;

    if (
      isCurrentYear &&
      selectedMonth?.length === 1 &&
      selectedDates?.length === datesToCheck
    ) {
      setIsCheckedDay(true);
    }
    if (
      isCurrentYear &&
      selectedMonth?.length === 1 &&
      selectedDates?.length !== datesToCheck
    ) {
      setIsCheckedDay(false);
    }
    if (!isCurrentYear || (!isCurrentMonth && selectedDates?.length < 31)) {
      setIsCheckedDay(false);
    }
    if (selectedDates?.length === 31) {
      setIsCheckedDay(true);
    }
    if (selectedDates?.length === 0) {
      setIsCheckedDay(false);
    }
  }, [selectedDates, startDayDate, endDayDate]);

  useEffect(() => {
    if (fullDay) {
      setMorningTime(dayjs().startOf("day"));
      setAfterTime(dayjs().endOf("day"));
    }
  }, [fullDay]);

  useEffect(() => {
    setSelectedDates([]);
    setSelectedMonth([]);
    setSelectedYears([]);
  }, [startDayDate, endDayDate]);

  // useEffect(() => {
  //   setSelectedDates([]);
  // }, [selectedMonth, selectedYears]);

  return (
    <>
      {scheduleMode && scheduleMode.id === 1 ? null : (
        <Grid>
          {scheduleMode?.id === 5 ? (
            <Box m={1} ml={2.5}>
              <Box>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isChecked}
                      onChange={(e) =>
                        handlerAllCheckYears(
                          e,
                          setSelectedYears,
                          setIsChecked,
                          years
                        )
                      }
                    />
                  }
                  label={t("events.validYear")} // 修改为你想要的多选框标签
                  sx={{
                    textAlign: "left",
                    font: "normal normal medium 18px/24px Roboto",
                    fontWeight: "bold",
                    letterSpacing: "10px",
                    color: "#474B4F",
                  }}
                />
              </Box>
              <Box ml={4}>
                {years.map((item) => {
                  return (
                    <FormControlLabel
                      key={item.id}
                      control={
                        <Checkbox
                          id={`permission-checkbox-${item.id}`}
                          checked={selectedYears?.includes(item.value)}
                          onChange={() =>
                            handleYearChange(
                              item.value,
                              selectedYears,
                              setSelectedYears
                            )
                          }
                        />
                      }
                      label={item.value}
                      sx={{
                        textAlign: "left",
                        font: "normal normal medium 18px/24px Roboto",
                        fontWeight: "bold",
                        letterSpacing: "10px",
                        color: "#474B4F",
                      }}
                    />
                  );
                })}
              </Box>
            </Box>
          ) : null}

          {scheduleMode.id === 4 || scheduleMode.id === 5 ? (
            <Box m={1} ml={2.5}>
              <Box>
                <FormControlLabel
                  disabled={
                    scheduleMode.id === 4 ? false : selectedYears.length === 0
                  }
                  control={
                    <Checkbox
                      checked={isCheckedMonth}
                      onChange={(e) =>
                        handlerAllCheckMonth(
                          e,
                          setSelectedMonth,
                          setIsCheckedMonth,
                          allMonth,
                          startDayDate,
                          endDayDate,
                          scheduleMode
                        )
                      }
                    />
                  }
                  label={t("events.validMonth")} // 修改为你想要的多选框标签
                />
              </Box>
              <Box ml={4}>
                {allMonth.map((item, index) => {
                  const endDateMonth = dayjs(endDayDate).month();
                  const startDateMonth = dayjs(startDayDate).month();
                  const isBeforeCurrentMonth =
                    dayjs(startDayDate).year() === dayjs(endDayDate).year() &&
                    (index < startDateMonth || index > endDateMonth);

                  const isDisabled =
                    (scheduleMode?.id === 5 &&
                      (selectedYears.length === 0 || isBeforeCurrentMonth)) ||
                    (scheduleMode?.id === 4 && isBeforeCurrentMonth);
                  return (
                    <FormControlLabel
                      key={item.id}
                      disabled={isDisabled}
                      control={
                        <Checkbox
                          id={`permission-checkbox-${item.id}`}
                          checked={selectedMonth?.includes(item.value)}
                          onChange={() =>
                            handlerMonth(
                              item.value,
                              selectedMonth,
                              setSelectedMonth
                            )
                          }
                        />
                      }
                      label={item?.value.substring(0, 3)}
                      sx={{
                        textAlign: "left",
                        font: "normal normal medium 18px/24px Roboto",
                        fontWeight: "bold",
                        letterSpacing: "10px",
                        color: "#474B4F",
                      }}
                    />
                  );
                })}
              </Box>
            </Box>
          ) : null}
          {scheduleMode.id === 4 || scheduleMode.id === 5 ? (
            <Box m={1} width={"60%"} ml={2.5}>
              <Box>
                <FormControlLabel
                  disabled={
                    scheduleMode.id === 4
                      ? selectedMonth?.length === 0
                      : selectedYears.length === 0 ||
                        selectedMonth?.length === 0
                  }
                  control={
                    <Checkbox
                      checked={isCheckedDay}
                      onChange={(e) =>
                        handlerAllCheckDay(
                          e,
                          setSelectedDates,
                          setIsCheckedDay,
                          formattedDaysArray,
                          startDayDate,
                          endDayDate,
                          scheduleMode,
                          selectedMonth,
                          monthMapping
                        )
                      }
                    />
                  }
                  label={t("events.validDate")} // 修改为你想要的多选框标签
                  sx={{
                    textAlign: "left",
                    font: "normal normal medium 18px/24px Roboto",
                    fontWeight: "bold",
                    letterSpacing: "10px",
                    color: "#474B4F",
                  }}
                />
              </Box>

              <Box ml={4}>
                <Grid container spacing={1} md={10}>
                  {formattedDaysArray?.map((item) => {
                    const endDateMonth = dayjs(endDayDate).month();
                    const endDate = dayjs(endDayDate).date();
                    const startDate = dayjs(startDayDate).date();

                    // Determine if only one month is selected and it is the current month
                    // const month = monthMapping[selectedMonth[0]];

                    // Determine if the current item (date) is disabled based on month conditions
                    const isDisabled =
                      (scheduleMode.id === 4 || scheduleMode.id === 5) &&
                      selectedMonth?.length === 1 &&
                      dayjs(startDayDate).year() === dayjs(endDayDate).year() &&
                      dayjs(startDayDate).month() === endDateMonth &&
                      (item.id < startDate || item.id > endDate);
                    return (
                      <Grid key={item.id} item xs={2} pr={1}>
                        <FormControlLabel
                          key={item.id}
                          disabled={
                            scheduleMode.id === 4 || scheduleMode.id === 5
                              ? isDisabled || selectedMonth?.length === 0
                              : selectedYears.length === 0 ||
                                selectedMonth?.length === 0
                          }
                          control={
                            <Checkbox
                              id={`permission-checkbox-${item.id}`}
                              checked={selectedDates?.includes(item.id)}
                              onChange={() =>
                                handleDayChange(
                                  item.id,
                                  selectedDates,
                                  setSelectedDates
                                )
                              }
                            />
                          }
                          label={item.value}
                          sx={{
                            textAlign: "left",
                            font: "normal normal medium 18px/24px Roboto",
                            fontWeight: "bold",
                            letterSpacing: "10px",
                            color: "#474B4F",
                          }}
                        />
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Box>
          ) : null}

          {/* weekDay 模块组件 */}
          {scheduleMode.id === 3 ? (
            <EveryDay
              selectedDays={selectedDays}
              startDayDate={startDayDate}
              endDayDate={endDayDate}
              setSelectedDays={setSelectedDays}
            />
          ) : null}

          {/* 开始时间-结束时间 */}
          <Box m={1}>
            <AllDay
              hanlderAllDay={hanlderAllDay}
              allday={allday}
              fullDay={fullDay}
              morningTime={morningTime}
              afterTime={afterTime}
              setMorningTime={setMorningTime}
              setAfterTime={setAfterTime}
            ></AllDay>
          </Box>
        </Grid>
      )}
    </>
  );
}

export default SetRepeatCondition;
