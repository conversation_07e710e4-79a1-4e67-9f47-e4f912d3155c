import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography, Box } from "@mui/material";
import RefushPie from "./RefushPie";
function RefreshSuccessRate(props) {
  const { dashBoardData } = props;
  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Box
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        }}>
        <Typography variant="h6" fontWeight={"bold"}>
          {t("dashboard.refresh_success_rate")}
        </Typography>
        <Grid p={0}>
          <div style={{ position: "relative", width: "180px" }}>
            <RefushPie
              // value={
              //   successCounts > 0
              //     ? Math.round((successCounts / priceChangeEvent?.length) * 100)
              //     : 0
              // }

              value={
                dashBoardData?.successRefreshPercentage
                  ? dashBoardData?.successRefreshPercentage
                  : 0
              }></RefushPie>
          </div>
        </Grid>
      </Box>
    </React.Fragment>
  );
}

export default RefreshSuccessRate;
