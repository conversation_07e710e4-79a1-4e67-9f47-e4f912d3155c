import React from "react";
import { useTranslation } from "react-i18next";
import { Grid, Typography } from "@mui/material";
import EpriceEsl from "@/assets/images/eprice_registered_esl.svg?react";
function GateWay(props) {
  const { dashBoardData } = props;

  const { t } = useTranslation();
  return (
    <React.Fragment>
      <Grid
        container
        alignItems={"center"}
        style={{
          display: "flex",
          flexDirection: "row",
          height: "100%",
          width: "100%",
        }}>
        <Grid item xs={6}>
          <EpriceEsl style={{ width: "100%" }} />
        </Grid>
        <Grid
          item
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "flex-start",
          }}>
          <Typography style={{ color: "gray", fontSize: "16px" }}>
            {t("dashboard.registered_ESL")}
          </Typography>
          <Typography
            style={{
              fontSize: "38px",
              fontWeight: "bold",
              textAlign: "center",
              marginBottom: "30px",
            }}>
            {dashBoardData?.screenCountOfOutlet}
          </Typography>
        </Grid>
      </Grid>
    </React.Fragment>
  );
}

export default GateWay;
