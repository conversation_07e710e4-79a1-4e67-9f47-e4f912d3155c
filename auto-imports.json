[{"from": "react", "name": "useState", "as": "useState"}, {"from": "react", "name": "useCallback", "as": "useCallback"}, {"from": "react", "name": "useMemo", "as": "useMemo"}, {"from": "react", "name": "useEffect", "as": "useEffect"}, {"from": "react", "name": "useRef", "as": "useRef"}, {"from": "react", "name": "useContext", "as": "useContext"}, {"from": "react", "name": "useReducer", "as": "useReducer"}, {"from": "react", "name": "useImperativeHandle", "as": "useImperativeHandle"}, {"from": "react", "name": "useDebugValue", "as": "useDebugValue"}, {"from": "react", "name": "useDeferredValue", "as": "useDeferredValue"}, {"from": "react", "name": "useLayoutEffect", "as": "useLayoutEffect"}, {"from": "react", "name": "useTransition", "as": "useTransition"}, {"from": "react", "name": "startTransition", "as": "startTransition"}, {"from": "react", "name": "useSyncExternalStore", "as": "useSyncExternalStore"}, {"from": "react", "name": "useInsertionEffect", "as": "useInsertionEffect"}, {"from": "react", "name": "useId", "as": "useId"}, {"from": "react", "name": "lazy", "as": "lazy"}, {"from": "react", "name": "memo", "as": "memo"}, {"from": "react", "name": "createRef", "as": "createRef"}, {"from": "react", "name": "forwardRef", "as": "forwardRef"}, {"from": "react-router-dom", "name": "useOutletContext", "as": "useOutletContext"}, {"from": "react-router-dom", "name": "useHref", "as": "useHref"}, {"from": "react-router-dom", "name": "useInRouterContext", "as": "useInRouterContext"}, {"from": "react-router-dom", "name": "useLocation", "as": "useLocation"}, {"from": "react-router-dom", "name": "useNavigationType", "as": "useNavigationType"}, {"from": "react-router-dom", "name": "useNavigate", "as": "useNavigate"}, {"from": "react-router-dom", "name": "useOutlet", "as": "useOutlet"}, {"from": "react-router-dom", "name": "useParams", "as": "useParams"}, {"from": "react-router-dom", "name": "useResolvedPath", "as": "useResolvedPath"}, {"from": "react-router-dom", "name": "useRoutes", "as": "useRoutes"}, {"from": "react-router-dom", "name": "useLinkClickHandler", "as": "useLinkClickHandler"}, {"from": "react-router-dom", "name": "useSearchParams", "as": "useSearchParams"}, {"from": "react-router-dom", "name": "Link", "as": "Link"}, {"from": "react-router-dom", "name": "NavLink", "as": "NavLink"}, {"from": "react-router-dom", "name": "Navigate", "as": "Navigate"}, {"from": "react-router-dom", "name": "Outlet", "as": "Outlet"}, {"from": "react-router-dom", "name": "Route", "as": "Route"}, {"from": "react-router-dom", "name": "Routes", "as": "Routes"}, {"from": "react-i18next", "name": "useTranslation", "as": "useTranslation"}, {"from": "@mui/material", "name": "Accordion", "as": "Accordion"}, {"from": "@mui/material", "name": "AccordionActions", "as": "AccordionActions"}, {"from": "@mui/material", "name": "AccordionDetails", "as": "AccordionDetails"}, {"from": "@mui/material", "name": "AccordionSummary", "as": "AccordionSummary"}, {"from": "@mui/material", "name": "<PERSON><PERSON>", "as": "<PERSON><PERSON>"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "AppBar", "as": "AppBar"}, {"from": "@mui/material", "name": "Autocomplete", "as": "Autocomplete"}, {"from": "@mui/material", "name": "Avatar", "as": "Avatar"}, {"from": "@mui/material", "name": "AvatarGroup", "as": "AvatarGroup"}, {"from": "@mui/material", "name": "Backdrop", "as": "Backdrop"}, {"from": "@mui/material", "name": "Badge", "as": "Badge"}, {"from": "@mui/material", "name": "BottomNavigation", "as": "BottomNavigation"}, {"from": "@mui/material", "name": "BottomNavigationAction", "as": "BottomNavigationAction"}, {"from": "@mui/material", "name": "Box", "as": "Box"}, {"from": "@mui/material", "name": "Breadcrumbs", "as": "Breadcrumbs"}, {"from": "@mui/material", "name": "<PERSON><PERSON>", "as": "<PERSON><PERSON>"}, {"from": "@mui/material", "name": "ButtonBase", "as": "ButtonBase"}, {"from": "@mui/material", "name": "ButtonGroup", "as": "ButtonGroup"}, {"from": "@mui/material", "name": "Card", "as": "Card"}, {"from": "@mui/material", "name": "CardActionArea", "as": "CardActionArea"}, {"from": "@mui/material", "name": "CardActions", "as": "CardActions"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "CardMedia", "as": "CardMedia"}, {"from": "@mui/material", "name": "Checkbox", "as": "Checkbox"}, {"from": "@mui/material", "name": "Chip", "as": "Chip"}, {"from": "@mui/material", "name": "CircularProgress", "as": "CircularProgress"}, {"from": "@mui/material", "name": "ClickAwayListener", "as": "ClickAwayListener"}, {"from": "@mui/material", "name": "Collapse", "as": "Collapse"}, {"from": "@mui/material", "name": "Container", "as": "Container"}, {"from": "@mui/material", "name": "CssBaseline", "as": "CssBaseline"}, {"from": "@mui/material", "name": "darkScrollbar", "as": "darkScrollbar"}, {"from": "@mui/material", "name": "Dialog", "as": "Dialog"}, {"from": "@mui/material", "name": "DialogActions", "as": "DialogActions"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "DialogContentText", "as": "DialogContentText"}, {"from": "@mui/material", "name": "DialogTitle", "as": "DialogTitle"}, {"from": "@mui/material", "name": "Divider", "as": "Divider"}, {"from": "@mui/material", "name": "Drawer", "as": "Drawer"}, {"from": "@mui/material", "name": "Fab", "as": "Fab"}, {"from": "@mui/material", "name": "Fade", "as": "Fade"}, {"from": "@mui/material", "name": "FilledInput", "as": "FilledInput"}, {"from": "@mui/material", "name": "FormControl", "as": "FormControl"}, {"from": "@mui/material", "name": "FormControlLabel", "as": "FormControlLabel"}, {"from": "@mui/material", "name": "FormGroup", "as": "FormGroup"}, {"from": "@mui/material", "name": "FormHelperText", "as": "FormHelperText"}, {"from": "@mui/material", "name": "FormLabel", "as": "FormLabel"}, {"from": "@mui/material", "name": "generateUtilityClass", "as": "generateUtilityClass"}, {"from": "@mui/material", "name": "generateUtilityClasses", "as": "generateUtilityClasses"}, {"from": "@mui/material", "name": "GlobalStyles", "as": "GlobalStyles"}, {"from": "@mui/material", "name": "Grid", "as": "Grid"}, {"from": "@mui/material", "name": "Grow", "as": "Grow"}, {"from": "@mui/material", "name": "Hidden", "as": "Hidden"}, {"from": "@mui/material", "name": "Icon", "as": "Icon"}, {"from": "@mui/material", "name": "IconButton", "as": "IconButton"}, {"from": "@mui/material", "name": "ImageList", "as": "ImageList"}, {"from": "@mui/material", "name": "ImageListItem", "as": "ImageListItem"}, {"from": "@mui/material", "name": "ImageListItemBar", "as": "ImageListItemBar"}, {"from": "@mui/material", "name": "Input", "as": "Input"}, {"from": "@mui/material", "name": "InputAdornment", "as": "InputAdornment"}, {"from": "@mui/material", "name": "InputBase", "as": "InputBase"}, {"from": "@mui/material", "name": "InputLabel", "as": "InputLabel"}, {"from": "@mui/material", "name": "LinearProgress", "as": "LinearProgress"}, {"from": "@mui/material", "name": "List", "as": "List"}, {"from": "@mui/material", "name": "ListItem", "as": "ListItem"}, {"from": "@mui/material", "name": "ListItemAvatar", "as": "ListItemAvatar"}, {"from": "@mui/material", "name": "ListItemButton", "as": "ListItemButton"}, {"from": "@mui/material", "name": "ListItemIcon", "as": "ListItemIcon"}, {"from": "@mui/material", "name": "ListItemSecondaryAction", "as": "ListItemSecondaryAction"}, {"from": "@mui/material", "name": "ListItemText", "as": "ListItemText"}, {"from": "@mui/material", "name": "ListSubheader", "as": "ListSubheader"}, {"from": "@mui/material", "name": "<PERSON><PERSON>", "as": "<PERSON><PERSON>"}, {"from": "@mui/material", "name": "MenuItem", "as": "MenuItem"}, {"from": "@mui/material", "name": "MenuList", "as": "MenuList"}, {"from": "@mui/material", "name": "MobileStepper", "as": "MobileStepper"}, {"from": "@mui/material", "name": "Modal", "as": "Modal"}, {"from": "@mui/material", "name": "NativeSelect", "as": "NativeSelect"}, {"from": "@mui/material", "name": "NoSsr", "as": "NoSsr"}, {"from": "@mui/material", "name": "OutlinedInput", "as": "OutlinedInput"}, {"from": "@mui/material", "name": "Pagination", "as": "Pagination"}, {"from": "@mui/material", "name": "PaginationItem", "as": "PaginationItem"}, {"from": "@mui/material", "name": "Paper", "as": "Paper"}, {"from": "@mui/material", "name": "Popover", "as": "Popover"}, {"from": "@mui/material", "name": "<PERSON><PERSON>", "as": "<PERSON><PERSON>"}, {"from": "@mui/material", "name": "Portal", "as": "Portal"}, {"from": "@mui/material", "name": "Radio", "as": "Radio"}, {"from": "@mui/material", "name": "RadioGroup", "as": "RadioGroup"}, {"from": "@mui/material", "name": "Rating", "as": "Rating"}, {"from": "@mui/material", "name": "ScopedCssBaseline", "as": "ScopedCssBaseline"}, {"from": "@mui/material", "name": "Select", "as": "Select"}, {"from": "@mui/material", "name": "Skeleton", "as": "Skeleton"}, {"from": "@mui/material", "name": "Slide", "as": "Slide"}, {"from": "@mui/material", "name": "Slide<PERSON>", "as": "Slide<PERSON>"}, {"from": "@mui/material", "name": "Snackbar", "as": "Snackbar"}, {"from": "@mui/material", "name": "SnackbarContent", "as": "SnackbarContent"}, {"from": "@mui/material", "name": "SpeedDial", "as": "SpeedDial"}, {"from": "@mui/material", "name": "SpeedDialAction", "as": "SpeedDialAction"}, {"from": "@mui/material", "name": "SpeedDialIcon", "as": "SpeedDialIcon"}, {"from": "@mui/material", "name": "<PERSON><PERSON>", "as": "<PERSON><PERSON>"}, {"from": "@mui/material", "name": "Step", "as": "Step"}, {"from": "@mui/material", "name": "StepButton", "as": "StepButton"}, {"from": "@mui/material", "name": "StepConnector", "as": "StepConnector"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "StepIcon", "as": "StepIcon"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "Stepper", "as": "Stepper"}, {"from": "@mui/material", "name": "SvgIcon", "as": "SvgIcon"}, {"from": "@mui/material", "name": "SwipeableDrawer", "as": "SwipeableDrawer"}, {"from": "@mui/material", "name": "Switch", "as": "Switch"}, {"from": "@mui/material", "name": "Tab", "as": "Tab"}, {"from": "@mui/material", "name": "Table", "as": "Table"}, {"from": "@mui/material", "name": "TableBody", "as": "TableBody"}, {"from": "@mui/material", "name": "TableCell", "as": "TableCell"}, {"from": "@mui/material", "name": "TableContainer", "as": "TableContainer"}, {"from": "@mui/material", "name": "TableFooter", "as": "TableFooter"}, {"from": "@mui/material", "name": "TableHead", "as": "TableHead"}, {"from": "@mui/material", "name": "TablePagination", "as": "TablePagination"}, {"from": "@mui/material", "name": "TableRow", "as": "TableRow"}, {"from": "@mui/material", "name": "TableSortLabel", "as": "TableSortLabel"}, {"from": "@mui/material", "name": "Tabs", "as": "Tabs"}, {"from": "@mui/material", "name": "TabScrollButton", "as": "TabScrollButton"}, {"from": "@mui/material", "name": "TextareaAutosize", "as": "TextareaAutosize"}, {"from": "@mui/material", "name": "TextField", "as": "TextField"}, {"from": "@mui/material", "name": "ToggleButton", "as": "ToggleButton"}, {"from": "@mui/material", "name": "ToggleButtonGroup", "as": "ToggleButtonGroup"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "<PERSON><PERSON><PERSON>", "as": "<PERSON><PERSON><PERSON>"}, {"from": "@mui/material", "name": "Typography", "as": "Typography"}, {"from": "@mui/material", "name": "Unstable_Grid2", "as": "Unstable_Grid2"}, {"from": "@mui/material", "name": "useAutocomplete", "as": "useAutocomplete"}, {"from": "@mui/material", "name": "useMediaQuery", "as": "useMediaQuery"}, {"from": "@mui/material", "name": "useScrollTrigger", "as": "useScrollTrigger"}, {"from": "@mui/material", "name": "Zoom", "as": "Zoom"}, {"from": "dayjs", "name": "default", "as": "dayjs"}]