import VisibilityIcon from "@a/images/View_Icon.svg?react";
import DeleteIcon from "@a/images/Delete_Icon.svg?react";
import EditIcon from "@a/images/Edit_Icon.svg?react";
import IconHandaler from "@c/IconHandaler";
import { useNavigate } from "react-router";
import AuthButton from "@/components/AuthButton.jsx";
export const getColums = (
  t,
  setConfirm,
  setId,
  setPreViewOpen,
  setPreViewItem
) => {
  const navigate = useNavigate();
  let columns = [
    {
      field: "name",
      headerName: t("template.name"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.name,
    },
    {
      field: "resolution",
      headerName: t("template.resolution"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => e.row.resolution,
    },
    {
      field: "screenOriantation",
      headerName: t("template.screen_direction"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) =>
        e.row.screenOriantation == "0"
          ? t("dictionary.vertical")
          : t("dictionary.horizontal"),
    },

    {
      field: "templateType",
      headerName: t("common.type"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) =>
        e.row.templateType == "0"
          ? t("common.system_default")
          : t("common.personal_template"),
    },

    {
      field: "templateJson",
      headerName: t("template.preview"),
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (e) => {
        return (
          <Tooltip title={t("common.preview")} arrow>
            <Button
              id="temppreview"
              sx={{ px: 0 }}
              onClick={() => {
                let templateJson = e.row.templateJson;
                if (templateJson) {
                  setPreViewOpen(true);
                  setPreViewItem(e.row);
                } else {
                  toast.info(t("screen.unbound_templates"));
                  getPicPreview(e);
                }
              }}>
              {t("common.preview")}
            </Button>
          </Tooltip>
        );
      },
    },
    {
      headerName: t("common.actions"),
      sortable: false,
      headerAlign: "center",
      align: "center",
      flex: 1,
      renderCell: (e) => (
        <IconHandaler>
          <AuthButton button="nt:nutag:template:info">
            <Tooltip title={t("common.view")} arrow>
              <VisibilityIcon
                id="viewtemplatelist"
                onClick={() => {
                  navigate("/view/template", {
                    state: {
                      id: e.row.id,
                      type: "view", // This is what you're trying to pass
                    },
                  });
                }}
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  height: "17px",
                  width: "20px",
                  padding: "2px",
                }}
              />
            </Tooltip>
          </AuthButton>
          <AuthButton button="nt:nutag:template:update">
            <Tooltip title={t("common.edit")} sx={{ marginLeft: 1 }}>
              <EditIcon
                onClick={() => {
                  navigate("/add/template", {
                    state: {
                      id: e.row.id,
                      type: "editor", // This is what you're trying to pass
                    },
                  });
                }}
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  paddingLeft: "5px",
                  height: "17px",
                  width: "20px",
                }}
              />
            </Tooltip>
          </AuthButton>
          <AuthButton button="nt:nutag:template:delete">
            <Tooltip title={t("common.delete")} sx={{ marginLeft: 1 }}>
              <DeleteIcon
                id="deletetemplist"
                onClick={() => {
                  setId(e.row.id);
                  setConfirm(true);
                }}
                style={{
                  alignSelf: "center",
                  paddingTop: "0px",
                  cursor: "pointer",
                  opacity: "0.6",
                  height: "17px",
                  width: "20px",
                  padding: "2px",
                }}
              />
            </Tooltip>
          </AuthButton>
        </IconHandaler>
      ),
    },
  ];

  return columns;
};
